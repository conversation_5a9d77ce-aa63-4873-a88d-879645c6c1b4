﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using tksrv.model;
using tkapi.Models;

namespace tksrv.dal
{
    public class DeviceDAL
    {
        public static Task<UserEntity> queryUser(string appid)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<UserEntity>("SELECT * FROM tb_user WHERE appid=@appid", 
                new { appid = appid });
        }

        public static Task<object> existDataDwin(string clientid)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<object>("SELECT 1 FROM tb_equip_dwin WHERE clientid=@clientid AND is_locked=0", 
                new { clientid = clientid });
        }

        public static Task updateDataDwinInfo(EquipDwinEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_equip_dwin(clientid,bound_code) SELECT @clientid,@bound_code; UPDATE tb_equip_dwin SET bound_code=@bound_code,version=@version,imei=@imei,iccid=@iccid,update_time=NOW() WHERE clientid=@clientid", model);
        }

        public static Task updateDataDwinReport(EquipDwinEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_equip_dwin(clientid,bound_code) SELECT @clientid,@bound_code; UPDATE tb_equip_dwin SET bound_code=@bound_code,report_data=@report_data,update_time=NOW() WHERE clientid=@clientid", model);
        }

        public static Task<EquipBdybEntity> queryDataBDYBInfo(string devno)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<EquipBdybEntity>("SELECT * FROM tb_equip_bdyb WHERE devno=@devno",
                new { devno = devno });
        }

        public static Task updateDataBDYBInfo(EquipBdybEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_equip_bdyb(devno,ptype) VALUES(@devno,@ptype); UPDATE tb_equip_bdyb SET brand=COALESCE(NULLIF(@brand,''),brand),telemetry=@telemetry,update_time=NOW() WHERE devno=@devno", model);
        }

        public static Task updateDataBDYBInfoOnline(EquipBdybEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_equip_bdyb(devno,ptype) VALUES(@devno,@ptype); UPDATE tb_equip_bdyb SET update_time=NOW() WHERE devno=@devno AND ptype=@ptype", model);
        }
    }
}
