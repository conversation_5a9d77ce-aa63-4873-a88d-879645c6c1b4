﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using tksrv.model;
using tkapi.Models;

namespace tksrv.dal
{
    public class QueueDAL
    {
        public static Task<DeviceEntity> queryMqttUser(string username)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<DeviceEntity>("SELECT * FROM tb_device WHERE username=@username OR clientid=@username",
                new { username = username });
        }

        public static Task updateMqttUser(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_device(clientid,username) VALUES(@clientid,@username); UPDATE tb_device SET password=@password,imei=@imei,imsi=@imsi,iccid=@iccid,update_time=NOW() WHERE username=@username", model);
        }

        public static Task<object> existMqttUser(string clientid)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<object>("SELECT 1 FROM tb_device WHERE clientid=@clientid OR username=@clientid LIMIT 1",
                new { clientid = clientid });
        }

        public static Task<DeviceEntity> queryMqtt(string clientid)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<DeviceEntity>("SELECT * FROM tb_device WHERE clientid=@clientid",
                new { clientid = clientid });
        }

        public static Task updateMqttConfig(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_device(clientid) VALUES(@clientid); UPDATE tb_device SET product=@product,version=@version,branchv=@branchv,attributes=@attributes WHERE clientid=@clientid", model);
        }

        public static Task updateMqttUserConfig(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("UPDATE tb_device SET imei=@imei,imsi=@imsi,iccid=@iccid WHERE username=@username OR clientid=@username", model);
        }

        public static Task updateMqttReport(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("UPDATE tb_device SET telemetry=@telemetry,update_time=NOW() WHERE clientid=@clientid", model);
        }

        public static Task updateMqttConnect(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("UPDATE tb_device SET connect_time=@connect_time,is_online=1 WHERE clientid=@clientid", model);
        }

        public static Task updateMqttDisconnect(DeviceEntity model)
        {
            return DapperManager.Master.ExecuteAsync("UPDATE tb_device SET disconnect_reason=@disconnect_reason,disconnect_time=@disconnect_time,is_online=0 WHERE clientid=@clientid", model);
        }

        public static Task<IEnumerable<DeviceEntity>> queryMqttHost(string hostid)
        {
            return DapperManager.Master.QueryAsync<DeviceEntity>("SELECT username FROM tb_device WHERE hostid=@hostid",
                new { hostid = hostid });
        }

        public static Task updateMqttHost(string username, string hostid)
        {
            return DapperManager.Master.ExecuteAsync("UPDATE tb_device SET hostid=@hostid WHERE username=@username OR clientid=@username",
                new { username = username, hostid = hostid  });
        }
    }
}
