﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using tksrv.model;

namespace tksrv.bll
{
    public class ConvertHBYBBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.hbyb[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                JToken ret = await getValue(rpath);
                var tv = ret.SelectToken($"$[?(@.dbh=='{ jsonp["addr_code"].Value<string>() }')]");
                if (tv != null)
                {
                    DateTime t = DateTimeHelper.Parse(tv["usetime"].Value<string>(), "yyyy-MM-dd HH:mm:ss");
                    switch (tv["type"].Value<int>())
                    {
                        case 1:
                            list.Add(new
                            {
                                param_id = 101,
                                param_number = tv["dbds"].Value<decimal>(),
                                gather_time = t
                            });
                            if (tv["ady"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 103,
                                    param_number = tv["ady"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["bdy"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 104,
                                    param_number = tv["bdy"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["cdy"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 105,
                                    param_number = tv["cdy"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["adl"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 106,
                                    param_number = tv["adl"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["bdl"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 107,
                                    param_number = tv["bdl"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["cdl"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 108,
                                    param_number = tv["cdl"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            if (tv["zglys"] != null)
                            {
                                list.Add(new
                                {
                                    param_id = 112,
                                    param_number = tv["zglys"].Value<decimal>(),
                                    gather_time = t
                                });
                            }
                            break;

                        case 2:
                            list.Add(new
                            {
                                param_id = 201,
                                param_number = tv["dbds"].Value<decimal>(),
                                gather_time = t
                            });
                            break;
                    }
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                int error = 0;
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.hbyb[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                JToken ret = await getValue(rpath);
                var tv = ret.SelectToken($"$[?(@.dbh=='{ jsonp["addr_code"].Value<string>() }')]");
                if (tv != null)
                {
                    foreach (var ctrl in jsonp["ctrl_list"].Value<string>().Split(','))
                    {
                        if (string.IsNullOrEmpty(ctrl))
                        {
                           continue;
                        }
                        bool result = true;
                        int id = int.Parse(ctrl.Split('=')[0]);
                        decimal vl = decimal.Parse(ctrl.Split('=')[1]);
                        if (tv["type"].Value<int>() == 1 && id == 133 && vl == 0)
                        {
                            var r = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + $"/ele/ele!kxcontrol.action?id={tv["id"].Value<int>()}&bid={JsonHelper.GetJsonValue<string>(rpath + ".projcode")}&type=2").PostAsync().ReceiveString());
                            if (r["status"] == null || r["status"].Value<string>() != "success")
                            {
                                result = false;
                            }
                        }
                        else if (tv["type"].Value<int>() == 1 && id == 133 && vl == 1)
                        {
                            var r = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + $"/ele/ele!kxcontrol.action?id={tv["id"].Value<int>()}&bid={JsonHelper.GetJsonValue<string>(rpath + ".projcode")}&type=1").PostAsync().ReceiveString());
                            if (r["status"] == null || r["status"].Value<string>() != "success")
                            {
                                result = false;
                            }
                        }
                        if (!result)
                        {
                            error = id;
                            break;
                        }
                    }
                }
                jsonRet.result = error;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JToken> getValue(string path)
        {
            string r = await DapperManager.Redis.StringGetAsync("hbyb." + JsonHelper.GetJsonValue<string>(path + ".projcode"));
            if (!string.IsNullOrEmpty(r))
            {
                return JToken.Parse(r);
            }
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName + "." + JsonHelper.GetJsonValue<string>(path + ".projcode");
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    r = await DapperManager.Redis.StringGetAsync("hbyb." + JsonHelper.GetJsonValue<string>(path + ".projcode"));
                    if (!string.IsNullOrEmpty(r))
                    {
                        return JToken.Parse(r);
                    }
                    var ret = JToken.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + $"/admin/release!alldata.action?bid={JsonHelper.GetJsonValue<string>(path + ".projcode")}").PostAsync().ReceiveString());
                    if (ret.Count() > 0)
                    {
                        await DapperManager.Redis.StringSetAsync("hbyb." + JsonHelper.GetJsonValue<string>(path + ".projcode"), ret.ToString(Formatting.None), TimeSpan.FromMilliseconds(10000));
                        return ret;
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return null;
        }
    }
}
