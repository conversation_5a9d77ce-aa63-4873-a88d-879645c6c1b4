﻿using System.Threading.Tasks;
using tksrv.model;
using tkapi.Models;

namespace tksrv.dal
{
    public class ProductDAL
    {
        public static Task<UserEntity> queryDeviceKey(string ekey)
        {
            return DapperManager.Master.QueryFirstOrDefaultAsync<UserEntity>("SELECT * FROM tb_user WHERE appid=@ekey",
                new { ekey = ekey });
        }

        public static Task updateDevice(GatewayEntity device)
        {
            return DapperManager.Master.ExecuteAsync("INSERT IGNORE INTO tb_gateway(cpud) VALUES(@cpud); UPDATE tb_gateway SET uuid=@uuid,update_time=NOW() WHERE cpud=@cpud",
                device);
        }
    }
}
