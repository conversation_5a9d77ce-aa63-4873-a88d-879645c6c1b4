﻿using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;
using NLog;
using Flurl.Http;
using tksrv.model;

namespace tksrv.bll
{
    public class ConvertGLXXBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.glxx[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                DateTime time = DateTime.Now;
                if ((jsonp["type_id"]?.Value<int>() ?? 5) == 5)
                {
                    string pars = $"vavboxid={jsonp["addr_code"].Value<string>()}&userid=&roomid=&op=setkd&btime=&etime=";
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/remotevavboxsearch?" + pars).WithHeader("Authorization", await getToken(rpath)).GetStringAsync());
                    if (ret["code"] != null && ret["code"].Value<int>() == 200 && !JValue.CreateNull().Equals(ret["data"]) && !JValue.CreateNull().Equals(ret["data"]["fmkd"]) && ret["data"]["fmkd"] != null)
                    {
                        list.Add(new
                        {
                            param_id = 533,
                            param_number = ret["data"]["fmkd"].Value<decimal>() > 0 ? 1 : 0,
                            gather_time = time
                        });
                    }
                    pars = $"vavboxid={jsonp["addr_code"].Value<string>()}&userid=&roomid=&op=setwd&btime=&etime=";
                    ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/remotevavboxsearch?" + pars).WithHeader("Authorization", await getToken(rpath)).GetStringAsync());
                    if (ret["code"] != null && ret["code"].Value<int>() == 200 && !JValue.CreateNull().Equals(ret["data"]) && !JValue.CreateNull().Equals(ret["data"]["setwd"]) && ret["data"]["setwd"] != null)
                    {
                        list.Add(new
                        {
                            param_id = 541,
                            param_number = ret["data"]["setwd"].Value<decimal>(),
                            gather_time = time
                        });
                    }
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                int error = 0;
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.glxx[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                foreach (var ctrl in jsonp["ctrl_list"].Value<string>().Split(','))
                {
                    if (string.IsNullOrEmpty(ctrl))
                    {
                        continue;
                    }
                    bool result = true;
                    int id = int.Parse(ctrl.Split('=')[0]);
                    decimal vl = decimal.Parse(ctrl.Split('=')[1]);
                    switch (id)
                    {
                        case 533:
                            {
                                string pars = $"vavboxid={jsonp["addr_code"].Value<string>()}&userid=&roomid=&op=setkd&val={(vl != 0 ? 100 : 0)}";
                                JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/remotesetvavbox?" + pars).WithHeader("Authorization", await getToken(rpath)).GetStringAsync());
                                if (ret["code"] == null || ret["code"].Value<int>() != 200)
                                {
                                    result = false;
                                }
                            }
                            break;

                        case 541:
                            {
                                string pars = $"vavboxid={jsonp["addr_code"].Value<string>()}&userid=&roomid=&op=setwd&val={vl}";
                                JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/remotesetvavbox?" + pars).WithHeader("Authorization", await getToken(rpath)).GetStringAsync());
                                if (ret["code"] == null || ret["code"].Value<int>() != 200)
                                {
                                    result = false;
                                }
                            }
                            break;
                    }
                    if (!result)
                    {
                        error = id;
                        break;
                    }
                }
                jsonRet.result = error;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<string> getToken(string path)
        {
            long expire = JsonHelper.GetJsonValue<long>(path + ".expire");
            string token = JsonHelper.GetJsonValue<string>(path + ".token");
            if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
            {
                return token;
            }
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName + "." + JsonHelper.GetJsonValue<string>(path + ".url");
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    expire = JsonHelper.GetJsonValue<long>(path + ".expire");
                    token = JsonHelper.GetJsonValue<string>(path + ".token");
                    if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
                    {
                        return token;
                    }
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + "/captcha").GetStringAsync());
                    if (ret["code"] == null || ret["code"].Value<int>() != 200)
                    {
                        return token;
                    }
                    string pars = $"username={JsonHelper.GetJsonValue<string>(path + ".username")}&password={JsonHelper.GetJsonValue<string>(path + ".password")}&code={ret["data"]["code"]}&token={ret["data"]["token"]}";
                    var resp = await (JsonHelper.GetJsonValue<string>(path + ".url") + "/login?" + pars).PostAsync();
                    ret = JObject.Parse(await resp.GetStringAsync());
                    if (ret["code"] != null && ret["code"].Value<int>() == 200)
                    {
                        JsonHelper.SetJsonValue(path + ".token", token = resp.Headers.FirstOrDefault(v => v.Name == "Authorization").Value);
                        JsonHelper.SetJsonValue(path + ".expire", expire = DateTimeHelper.GetTimeSpan() / 1000 + 60 * 60 * 24 - 60 * 5);
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return token;
        }
    }
}
