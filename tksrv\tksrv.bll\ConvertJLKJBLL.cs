﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using tksrv.model;

namespace tksrv.bll
{
    public class ConvertJLKJBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.jlkj[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                DateTime time = DateTime.Now;
                JObject ret = await getValue(rpath);
                if (ret != null)
                {
                    var tv = ret.SelectToken($"data.page.records[?(@.id=='{ jsonp["addr_code"].Value<string>() }')]");
                    if (tv != null)
                    {
                        time = DateTimeHelper.Parse(tv["dataTimestamp"].Value<long>());
                        list.Add(new
                        {
                            param_id = 101,
                            param_number = tv["etotal1"].Value<decimal>(),
                            gather_time = time
                        });
                    }
                }
                if (ret != null && JsonHelper.GetJsonValueDefault(rpath + ".riv", false))
                {
                    ret = await getValueIV(rpath, jsonp["addr_code"].Value<string>());
                    if (ret != null)
                    {
                        list.Add(new
                        {
                            param_id = 103,
                            param_number = ret["data"]["uAc1"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 104,
                            param_number = ret["data"]["uAc2"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 105,
                            param_number = ret["data"]["uAc3"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 106,
                            param_number = ret["data"]["iAc1"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 107,
                            param_number = ret["data"]["iAc2"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 108,
                            param_number = ret["data"]["iAc3"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 108,
                            param_number = ret["data"]["iAc3"].Value<decimal>(),
                            gather_time = time
                        });
                        list.Add(new
                        {
                            param_id = 109,
                            param_number = ret["data"]["pac"].Value<decimal>() * (ret["data"]["pacStr"].Value<string>().ToUpper() == "G" ? 1000000 : (ret["data"]["pacStr"].Value<string>().ToUpper() == "M" ? 1000 : 1)),
                            gather_time = time
                        });
                    }
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JObject> getValue(string path)
        {
            string r = await DapperManager.Redis.StringGetAsync("jlkj." + JsonHelper.GetJsonValue<string>(path + ".keyid"));
            if (!string.IsNullOrEmpty(r))
            {
                return JObject.Parse(r);
            }
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName;
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    r = await DapperManager.Redis.StringGetAsync("jlkj." + JsonHelper.GetJsonValue<string>(path + ".keyid"));
                    if (!string.IsNullOrEmpty(r))
                    {
                        return JObject.Parse(r);
                    }
                    string body = "{\"pageNo\":1,\"pageSize\":100}";
                    string date = DateTime.Now.ToUniversalTime().ToString("r");
                    string md5v = body.EX_Continue(v =>
                    {
                        using (var md5 = MD5.Create())
                        {
                            return Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes(v)));
                        }
                    });
                    string auth = $"POST\n{md5v}\napplication/json\n{date}\n/v1/api/inverterList".EX_Continue(v =>
                    {
                        using (var sha1 = new HMACSHA1())
                        {
                            sha1.Key = Encoding.UTF8.GetBytes(JsonHelper.GetJsonValue<string>(path + ".keysecret"));
                            return $"API {JsonHelper.GetJsonValue<string>(path + ".keyid")}:{Convert.ToBase64String(sha1.ComputeHash(Encoding.UTF8.GetBytes(v)))}";
                        }
                    });
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + "/v1/api/inverterList")
                        .WithHeader("Content-MD5", md5v)
                        .WithHeader("Date", date)
                        .WithHeader("Authorization", auth)
                        .PostJsonStringAsync(body).ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        await DapperManager.Redis.StringSetAsync("jlkj." + JsonHelper.GetJsonValue<string>(path + ".keyid"), ret.ToString(Formatting.None), TimeSpan.FromMilliseconds(10000));
                        return ret;
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return null;
        }

        public static async Task<JObject> getValueIV(string path, string id)
        {
            JObject json = null;
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName + ".iv";
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    string body = $"{{\"id\":\"{id}\"}}";
                    string date = DateTime.Now.ToUniversalTime().ToString("r");
                    string md5v = body.EX_Continue(v =>
                    {
                        using (var md5 = MD5.Create())
                        {
                            return Convert.ToBase64String(md5.ComputeHash(Encoding.UTF8.GetBytes(v)));
                        }
                    });
                    string auth = $"POST\n{md5v}\napplication/json\n{date}\n/v1/api/inverterDetail".EX_Continue(v =>
                    {
                        using (var sha1 = new HMACSHA1())
                        {
                            sha1.Key = Encoding.UTF8.GetBytes(JsonHelper.GetJsonValue<string>(path + ".keysecret"));
                            return $"API {JsonHelper.GetJsonValue<string>(path + ".keyid")}:{Convert.ToBase64String(sha1.ComputeHash(Encoding.UTF8.GetBytes(v)))}";
                        }
                    });
                    while(await DapperManager.Redis.KeyExistsAsync("jlkj.apitime"))
                    {
                        await Task.Delay(100);
                    }
                    await DapperManager.Redis.StringSetAsync("jlkj.apitime", "", TimeSpan.FromMilliseconds(2000));
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + "/v1/api/inverterDetail")
                        .WithHeader("Content-MD5", md5v)
                        .WithHeader("Date", date)
                        .WithHeader("Authorization", auth)
                        .PostJsonStringAsync(body).ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        json = ret;
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return json;
        }
    }
}
