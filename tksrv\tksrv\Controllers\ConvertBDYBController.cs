﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/convertbdyb")]
    public class ConvertBDYBController : ControllerBase
    {
        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertBDYBBLL.getMeterValue(json));
        }

        [HttpPost, Route("setMeterValue")]
        public async Task<JsonResult> setMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertBDYBBLL.setMeterValue(json));
        }
    }
}
