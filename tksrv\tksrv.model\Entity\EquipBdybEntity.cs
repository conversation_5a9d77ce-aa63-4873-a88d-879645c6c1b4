using System;

namespace tkapi.Models
{
    /// <summary>
    /// 北电云表设备表实体
    /// </summary>
    public class EquipBdybEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        public int sysid { get; set; }

        /// <summary>
        /// 设备编号，唯一索引
        /// </summary>
        public string devno { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型
        /// </summary>
        public string ptype { get; set; } = string.Empty;

        /// <summary>
        /// 品牌
        /// </summary>
        public string brand { get; set; } = "";

        /// <summary>
        /// 推送Unilink
        /// </summary>
        public string unilink { get; set; } = "default";

        /// <summary>
        /// 变量
        /// </summary>
        public string v1_telemetry { get; set; }

        /// <summary>
        /// 版本属性
        /// </summary>
        public string attributes { get; set; }

        /// <summary>
        /// 事件
        /// </summary>
        public string events { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; } = DateTime.Now;
    }
} 