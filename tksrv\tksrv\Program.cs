using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Heyme;
using System;
using System.Net;
using System.Text;
using tksrv.model;
using tksrv.lib;
using NLog;
using NLog.Config;

namespace tksrv
{
    public class Program
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();

        static Program()
        {
            string resp = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config.json");
            if (!System.IO.File.Exists(resp))
            {
                System.IO.File.WriteAllBytes(resp, Properties.Resources.config);
            }
        }

        public static void Main(string[] args)
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            LogManager.Configuration = new LoggingConfiguration().WithAddRule(NLog.LogLevel.Trace, NLog.LogLevel.Info, new NLog.Targets.FileTarget()
                {
                    Layout = "[${longdate} ${level:uppercase=true}] ${message}",
                    FileName = "${basedir}/logs/${shortdate}.log"

                })
                .WithAddRule(NLog.LogLevel.Warn, NLog.LogLevel.Fatal, new NLog.Targets.FileTarget()
                {
                    Layout = "[${longdate} ${level:uppercase=true}] ${callsite} ${message} ${newline} ${exception:format=tostring}",
                    FileName = "${basedir}/logs/${shortdate}.log"
                });
            JsonConvert.DefaultSettings = new Func<JsonSerializerSettings>(() =>
                {
                    return new JsonSerializerSettings()
                    {
                        DateFormatString = "yyyy-MM-dd HH:mm:ss.fff"
                    };
                });
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler((s, ex) => { logger.Error(ex.ExceptionObject); });
            DapperManager.Initialize(JsonHelper.GetJsonValue<string>("connStr"), JsonHelper.GetJsonValue<string>("kvdbStr"));
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.UseContentRoot(AppDomain.CurrentDomain.BaseDirectory);
                    webBuilder.UseUrls(Heyme.JsonHelper.GetJsonValue<string>("bindUrl"));
                    webBuilder.UseStartup<Startup>();
                });
    }
}
