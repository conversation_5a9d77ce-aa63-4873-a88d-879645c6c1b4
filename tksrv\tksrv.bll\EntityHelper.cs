using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using tkapi.Models;
using tksrv.model;
using System;

namespace tksrv.bll
{
    /// <summary>
    /// 实体转换辅助类，用于新旧数据结构的转换
    /// </summary>
    public static class EntityHelper
    {
        /// <summary>
        /// 从新的EquipBdybEntity获取grid_param值
        /// </summary>
        public static string GetGridParam(EquipBdybEntity entity)
        {
            if (string.IsNullOrEmpty(entity?.v1_telemetry))
                return "";
            
            try
            {
                var telemetry = JObject.Parse(entity.v1_telemetry);
                return telemetry["grid_param"]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 从新的EquipBdybEntity获取ctrl_param_ir值
        /// </summary>
        public static string GetCtrlParamIr(EquipBdybEntity entity)
        {
            if (string.IsNullOrEmpty(entity?.v1_telemetry))
                return "";
            
            try
            {
                var telemetry = JObject.Parse(entity.v1_telemetry);
                return telemetry["ctrl_param_ir"]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 从新的EquipBdybEntity获取ctrl_param_sw值
        /// </summary>
        public static string GetCtrlParamSw(EquipBdybEntity entity)
        {
            if (string.IsNullOrEmpty(entity?.v1_telemetry))
                return "";
            
            try
            {
                var telemetry = JObject.Parse(entity.v1_telemetry);
                return telemetry["ctrl_param_sw"]?.ToString() ?? "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 从新的EquipBdybEntity获取grid_time值
        /// </summary>
        public static DateTime GetGridTime(EquipBdybEntity entity)
        {
            if (string.IsNullOrEmpty(entity?.v1_telemetry))
                return DateTime.Now;
            
            try
            {
                var telemetry = JObject.Parse(entity.v1_telemetry);
                var gridTimeStr = telemetry["grid_time"]?.ToString();
                if (DateTime.TryParse(gridTimeStr, out DateTime gridTime))
                    return gridTime;
            }
            catch
            {
                // ignore
            }
            
            return entity?.update_time ?? DateTime.Now;
        }

        /// <summary>
        /// 创建新的EquipBdybEntity并设置telemetry字段
        /// </summary>
        public static EquipBdybEntity CreateBdybEntity(string devno, string ptype, string brand = "", 
            string gridParam = "", string ctrlParamIr = "", string ctrlParamSw = "")
        {
            var telemetryObj = new JObject();
            
            if (!string.IsNullOrEmpty(gridParam))
                telemetryObj["grid_param"] = gridParam;
            if (!string.IsNullOrEmpty(ctrlParamIr))
                telemetryObj["ctrl_param_ir"] = ctrlParamIr;
            if (!string.IsNullOrEmpty(ctrlParamSw))
                telemetryObj["ctrl_param_sw"] = ctrlParamSw;
            
            telemetryObj["grid_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            telemetryObj["ctrl_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            return new EquipBdybEntity
            {
                devno = devno,
                ptype = ptype,
                brand = brand,
                v1_telemetry = telemetryObj.ToString(Formatting.None).Replace("\\r\\n", "").Replace("\\n", ""),
                update_time = DateTime.Now
            };
        }

        /// <summary>
        /// 更新EquipBdybEntity的telemetry字段中的特定参数
        /// </summary>
        public static void UpdateTelemetryParam(EquipBdybEntity entity, string paramName, string paramValue)
        {
            try
            {
                JObject telemetry;
                if (string.IsNullOrEmpty(entity.v1_telemetry))
                {
                    telemetry = new JObject();
                }
                else
                {
                    telemetry = JObject.Parse(entity.v1_telemetry);
                }

                telemetry[paramName] = paramValue;
                telemetry[paramName.Contains("grid") ? "grid_time" : "ctrl_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                
                entity.v1_telemetry = telemetry.ToString(Formatting.None).Replace("\\r\\n", "").Replace("\\n", "");
                entity.update_time = DateTime.Now;
            }
            catch
            {
                // 如果解析失败，创建新的telemetry对象
                var telemetryObj = new JObject
                {
                    [paramName] = paramValue,
                    [paramName.Contains("grid") ? "grid_time" : "ctrl_time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                entity.v1_telemetry = telemetryObj.ToString(Formatting.None).Replace("\\r\\n", "").Replace("\\n", "");
                entity.update_time = DateTime.Now;
            }
        }
    }
}