﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace tksrv.bll
{
    public class ConvertQSBJBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.qsbj[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string key = JsonHelper.GetJsonValue<string>(rpath + ".key");
                string url = JsonHelper.GetJsonValue<string>(rpath + ".url");
                JObject ret = JObject.Parse(await (url + "/meterInfo").PostJsonAsync(new
                {
                    data = new
                    {
                        meterCodes = new string[] { jsonp["addr_code"].Value<string>() },
                        userCodes = new string[0]
                    },
                    key = key
                }).ReceiveString());
                if (ret["code"] != null && ret["code"].Value<int>() == 1)
                {
                    DateTime time = DateTime.MinValue;
                    foreach (var it in ret["data"])
                    {
                        list.Add(new
                        {
                            param_id = 201,
                            param_number = it["curNum"].Value<decimal>(),
                            gather_time = it["curTime"].Value<DateTime>()
                        });
                        break;
                    }
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }
    }
}
