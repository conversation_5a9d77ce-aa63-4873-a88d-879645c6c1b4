﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <Version>3.1.2</Version>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>..\key.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Heyme.Core" Version="1.2.7" />
    <PackageReference Include="Heyme.Data.Http" Version="1.0.2" />
    <PackageReference Include="Heyme.Enum.Device" Version="1.1.22" />
    <PackageReference Include="Heyme.Utils.Logger" Version="1.0.4" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="System.Drawing.Common" Version="4.7.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.34.0" />
    <PackageReference Include="System.Text.Json" Version="6.0.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\tksrv.dal\tksrv.dal.csproj" />
    <ProjectReference Include="..\tksrv.lib\tksrv.lib.csproj" />
    <ProjectReference Include="..\tksrv.model\tksrv.model.csproj" />
  </ItemGroup>

</Project>
