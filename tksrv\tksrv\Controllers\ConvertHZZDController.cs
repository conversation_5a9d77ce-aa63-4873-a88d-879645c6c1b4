﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/converthzzd")]
    public class ConvertHZZDController : ControllerBase
    {
        [HttpPost, Route("interface/getDataValue.action")]
        public async Task<JsonResult> getDataValue(string param)
        {
            return new JsonResult(await ConvertHZZDBLL.getDataValue(JObject.Parse(param)), new JsonSerializerSettings
            {
                ContractResolver = null
             });
        }

        [HttpPost, Route("interface/getDataValue1.action")]
        public async Task<JsonResult> getDataValue1(string param)
        {
            return new JsonResult(await ConvertHZZDBLL.getDataValue(JObject.Parse(param)), new JsonSerializerSettings
            {
                ContractResolver = null
             });
        }

        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertHZZDBLL.getMeterValue(json));
        }
    }
}
