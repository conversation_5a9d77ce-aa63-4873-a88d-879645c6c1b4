﻿using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using tksrv.dal;
using tksrv.model;
using tkapi.Models;
using Org.BouncyCastle.Crypto.Modes;
using NLog;
using Flurl.Http;
using System.Threading;

namespace tksrv.bll
{
    public class ConvertBDYBBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.bdyb[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                var dmod = await DeviceDAL.queryDataBDYBInfo(jsonp["addr_code"].Value<string>());
                string devno = dmod?.devno ?? jsonp["addr_code"].Value<string>(), ptype = dmod?.ptype ?? DeviceBDYBBLL.DVAL_PTYPE;
                if ((jsonp["type_id"]?.Value<int>() ?? 10) == 10)
                {
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await getToken(rpath)).PostJsonAsync(new
                    {
                        productType = ptype,
                        devNo = devno,
                        methodType = "pro07_standard_read",
                        methodContent = "grid_param_datablock"
                    }).ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JToken r = null;
                        string topic = ret["commandId"].Value<string>();
                        try
                        {
                            var tcs = new TaskCompletionSource<JToken>();
                            await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                            {
                                if (!string.IsNullOrEmpty(message))
                                {
                                    tcs.TrySetResult(JToken.Parse(message));
                                }
                            });
                            if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                            {
                                r = tcs.Task.Result;
                            }
                        }
                        finally
                        {
                            await DapperManager.Redis.UnsubscribeAsync(topic);
                        }
                        if (r != null && r["status"]?.Value<int>() == 0)
                        {
                            var entity = EntityHelper.CreateBdybEntity(devno, ptype, "",
                                JObject.FromObject(new { total_energy = r["result"]["combinedActiveTotalEnergy"].Value<decimal>() }).ToString(),
                                "",
                                JObject.FromObject(new { power = r["result"]["switchType"].Value<int>() != 0 ? 0 : 1 }).ToString());
                            await DeviceDAL.updateDataBDYBInfo(entity);
                            jsonRet.result = true;
                        }
                    }
                    var model = await DeviceDAL.queryDataBDYBInfo(devno);
                    list.Add(new
                    {
                        param_id = 1001,
                        param_number = JsonHelper.GetJsonStrValueDefault<decimal>(EntityHelper.GetGridParam(model), "total_energy", 0),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1037,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamSw(model), "power", 0),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1033,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamSw(model), "power", 0) != 0 ? JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "kgzt", 0) : 0,
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1041,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "sdwd", 26),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1039,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "yxms", 0),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1040,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "fsdw", 0),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                    list.Add(new
                    {
                        param_id = 1038,
                        param_number = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "sffs", 0),
                        gather_time = EntityHelper.GetGridTime(model)
                    });
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                int error = 0;
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.bdyb[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                Dictionary<int, decimal> ctrls = new Dictionary<int, decimal>();
                foreach (var ctrl in jsonp["ctrl_list"].Value<string>().Split(','))
                {
                    if (string.IsNullOrEmpty(ctrl) || ctrls.ContainsKey(int.Parse(ctrl.Split('=')[0])))
                    {
                        continue;
                    }
                    ctrls.Add(int.Parse(ctrl.Split('=')[0]), decimal.Parse(ctrl.Split('=')[1]));
                }
                var dmod = await DeviceDAL.queryDataBDYBInfo(jsonp["addr_code"].Value<string>());
                string devno = dmod?.devno ?? jsonp["addr_code"].Value<string>(), ptype = dmod?.ptype ?? DeviceBDYBBLL.DVAL_PTYPE, brand = !string.IsNullOrEmpty(jsonp["brand"]?.Value<string>()) ? jsonp["brand"]?.Value<string>() : dmod.brand;
                if (error == 0 && ctrls.ContainsKey(1037))
                {
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await getToken(rpath)).PostJsonAsync(new
                    {
                        productType = ptype,
                        devNo = devno,
                        methodType = "air_conditioning_control",
                        methodContent = ctrls[1037] != 0 ? "switch_on" : "switch_off"
                    }).ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JToken r = null;
                        string topic = ret["commandId"].Value<string>();
                        try
                        {
                            var tcs = new TaskCompletionSource<JToken>();
                            await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                            {
                                if (!string.IsNullOrEmpty(message))
                                {
                                    tcs.TrySetResult(JToken.Parse(message));
                                }
                            });
                            if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                            {
                                r = tcs.Task.Result;
                            }
                        }
                        finally
                        {
                            await DapperManager.Redis.UnsubscribeAsync(topic);
                        }
                        if (r != null && r["status"]?.Value<int>() == 0)
                        {
                            var entity = EntityHelper.CreateBdybEntity(devno, ptype, "", "", "",
                                JObject.FromObject(new { power = ctrls[1037] }).ToString());
                            await DeviceDAL.updateDataBDYBInfo(entity);
                            jsonRet.result = true;
                        }
                    }
                    else
                    {
                        error = 1037;
                    }
                }
                if (error == 0 && (ctrls.ContainsKey(1033) || ctrls.ContainsKey(1041) || ctrls.ContainsKey(1039) || ctrls.ContainsKey(1040) || ctrls.ContainsKey(1038)))
                {
                    JObject pars = JObject.FromObject(new
                    {
                        productType = ptype,
                        devNo = devno,
                        methodType = "air_controller",
                        methodContent = "infrared_set",
                        @params = new
                        {
                            firmCode = brand
                        }
                    });
                    int kgzt = ctrls.ContainsKey(1033) ? (int)ctrls[1033] : JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(dmod), "kgzt", 0);
                    int sdwd = ctrls.ContainsKey(1041) ? (int)ctrls[1041] : JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(dmod), "sdwd", 26);
                    int yxms = ctrls.ContainsKey(1039) ? (int)ctrls[1039] : JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(dmod), "yxms", 0);
                    int fsdw = ctrls.ContainsKey(1040) ? (int)ctrls[1040] : JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(dmod), "fsdw", 0);
                    int sffs = ctrls.ContainsKey(1038) ? (int)ctrls[1038] : JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(dmod), "sffs", 0);
                    switch (kgzt)
                    {
                        case 0:
                            pars["params"]["switchOnOff"] = "00";
                            break;

                        case 1:
                            pars["params"]["switchOnOff"] = "01";
                            break;
                    }
                    pars["params"]["temperature"] = sdwd;
                    switch (yxms)
                    {
                        case 0:
                            pars["params"]["airMode"] = "04";
                            break;

                        case 2:
                            pars["params"]["airMode"] = "02";
                            break;

                        case 3:
                            pars["params"]["airMode"] = "01";
                            break;

                        case 4:
                            pars["params"]["airMode"] = "08";
                            break;
                    }
                    switch (fsdw)
                    {
                        case 0:
                            pars["params"]["windSpeed"] = "00";
                            break;

                        case 1:
                            pars["params"]["windSpeed"] = "01";
                            break;

                        case 2:
                            pars["params"]["windSpeed"] = "02";
                            break;

                        case 3:
                            pars["params"]["windSpeed"] = "03";
                            break;
                    }
                    switch (sffs)
                    {
                        case 0:
                            pars["params"]["windMode"] = "00";
                            break;

                        case 1:
                            pars["params"]["windMode"] = "01";
                            break;

                        case 2:
                            pars["params"]["windMode"] = "02";
                            break;

                        case 3:
                            pars["params"]["windMode"] = "03";
                            break;
                    }
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await getToken(rpath))
                        .PostJsonStringAsync(pars.ToString()).ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JToken r = null;
                        string topic = ret["commandId"].Value<string>();
                        try
                        {
                            var tcs = new TaskCompletionSource<JToken>();
                            await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                            {
                                if (!string.IsNullOrEmpty(message))
                                {
                                    tcs.TrySetResult(JToken.Parse(message));
                                }
                            });
                            if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                            {
                                r = tcs.Task.Result;
                            }
                        }
                        finally
                        {
                            await DapperManager.Redis.UnsubscribeAsync(topic);
                        }
                        if (r != null && r["status"]?.Value<int>() == 0)
                        {
                            var entity = EntityHelper.CreateBdybEntity(devno, ptype, brand, "",
                                JObject.FromObject(new
                                {
                                    kgzt = kgzt,
                                    sdwd = sdwd,
                                    yxms = yxms,
                                    fsdw = fsdw,
                                    sffs = sffs
                                }).ToString(), "");
                            await DeviceDAL.updateDataBDYBInfo(entity);
                            jsonRet.result = true;
                        }
                    }
                    else
                    {
                        error = 1033;
                    }
                }
                jsonRet.result = error;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<string> getToken(string path)
        {
            long expire = JsonHelper.GetJsonValue<long>(path + ".expire");
            string token = JsonHelper.GetJsonValue<string>(path + ".token");
            if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
            {
                return token;
            }
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName;
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    expire = JsonHelper.GetJsonValue<long>(path + ".expire");
                    token = JsonHelper.GetJsonValue<string>(path + ".token");
                    if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
                    {
                        return token;
                    }
                    string urls = $"userName={JsonHelper.GetJsonValue<string>(path + ".username")}&time={DateTimeHelper.GetTimeSpan() / 1000}&num={new Random().Next(0, int.MaxValue)}";
                    urls += "&sign=" + EncryptHelper.EncodeSHA256(Encoding.UTF8.GetBytes(urls + $"&key={JsonHelper.GetJsonValue<string>(path + ".key")}")).ToLower();
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + "/api/sys/login?" + urls).PostUrlEncodedAsync("").ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JsonHelper.SetJsonValue(path + ".token", token = ret["token"].Value<string>());
                        JsonHelper.SetJsonValue(path + ".expire", expire = DateTimeHelper.GetTimeSpan() / 1000 + ret["expire"].Value<long>() - 10);
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return token;
        }
    }
}
