﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/devicebdyb")]
    public class DeviceBDYBController : ControllerBase
    {
        [HttpGet, Route("login")]
        public async Task<JsonResult> login(string appid, long timespan, string noncstr, string signature)
        {
            return new JsonResult(await ProductBLL.login(appid, timespan, noncstr, signature));
        }

        [HttpPost, Route("read")]
        public async Task<JsonResult> read([FromHeader] string token)
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                return new JsonResult(await DeviceBDYBBLL.read(JObject.Parse(await reader.ReadToEndAsync()), token));
            }
        }

        [HttpPost, Route("setpower")]
        public async Task<JsonResult> setpower([FromHeader] string token)
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                return new JsonResult(await DeviceBDYBBLL.setpower(JObject.Parse(await reader.ReadToEndAsync()), token));
            }
        }

        [HttpPost, Route("setparam")]
        public async Task<JsonResult> setparam([FromHeader] string token)
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                return new JsonResult(await DeviceBDYBBLL.setparam(JObject.Parse(await reader.ReadToEndAsync()), token));
            }
        }

        [HttpPost, Route("beidian-response")]
        public async Task response()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                await DeviceBDYBBLL.response(JObject.Parse(await reader.ReadToEndAsync()));
            }
        }
    }
}
