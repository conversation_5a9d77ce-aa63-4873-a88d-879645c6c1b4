﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Heyme.Enum.Device;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using tksrv.dal;
using tksrv.model;
using tkapi.Models;

namespace tksrv.bll
{
    public class ConvertMQTTBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getMeter(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.mqtt[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                var user = await QueueDAL.queryMqttUser(icid);
                if (user == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                var mqtt = await QueueDAL.queryMqtt(icid);
                jsonRet.result = new
                {
                    version = JsonHelper.GetJsonStrValueDefault(mqtt?.attributes, "version", ""),
                    hostid = user.hostid,
                    ftype = JsonHelper.GetJsonStrValueDefault(mqtt?.attributes, "boot.ftype", ""),
                    ftime = JsonHelper.GetJsonStrValueDefault(mqtt?.attributes, "boot.ftime", ""),
                    ncsq = JsonHelper.GetJsonStrValueDefault(mqtt?.attributes, "device.ncsq", 0),
                    iccid = JsonHelper.GetJsonStrValueDefault(mqtt?.attributes, "device.iccid", "")
                };
                string upid = "u" + Guid.NewGuid().ToString("N");
                await publish(rpath, $"device/{icid}/config/query", JObject.FromObject(new { uuid = upid }).ToString(Formatting.None));
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setMeter(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.mqtt[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                var user = await QueueDAL.queryMqttUser(icid);
                if (user == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                await QueueDAL.updateMqttHost(icid, jsonp["hostid"].Value<string>());
                await broadcast(rpath, new string[] { $"device/{icid}/broad" }, "");
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.mqtt[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                if (await QueueDAL.existMqttUser(icid) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                var mqtt = await QueueDAL.queryMqtt(icid);
                if (mqtt != null && !string.IsNullOrEmpty(mqtt.telemetry))
                {
                    JObject ret = JObject.Parse(mqtt.telemetry);
                    foreach (var item in ret["value"])
                    {
                        list.Add(new
                        {
                            param_id = item["id"].Value<int>(),
                            param_number = item["num"].Value<decimal>(),
                            gather_time = ret["gtime"].Value<DateTime>()
                        });
                    }
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> relay(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.mqtt[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                if (await QueueDAL.existMqttUser(icid) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                JsonRetModel r = null;
                string upid = "u" + Guid.NewGuid().ToString("N");
                JObject reqs = JObject.FromObject(new { uuid = upid, data = jsonp["send_data"].Value<string>() });
                if (jsonp["recv_time"] != null)
                {
                    reqs["rtime"] = jsonp["recv_time"].Value<int>();
                }
                if (jsonp["byte_time"] != null)
                {
                    reqs["btime"] = jsonp["byte_time"].Value<int>();
                }
                string topic = $"device/{upid}/respone/relay";
                try 
                {
                    var tcs = new TaskCompletionSource<JsonRetModel>();
                    await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                    {
                        if (!string.IsNullOrEmpty(message))
                        {
                            tcs.TrySetResult(JsonRetModel.Parse(message));
                        }
                    });
                    await publish(rpath, $"device/{icid}/request/relay", reqs.ToString(Formatting.None));
                    if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                    {
                        r = tcs.Task.Result;
                    }
                }
                finally
                {
                    await DapperManager.Redis.UnsubscribeAsync(topic);
                }
                if (r != null && r.GetResult())
                {
                    jsonRet.result = r.Value<string>();
                }
                if (jsonRet.result == null)
                {
                    return JsonRetModel.ValErrorExpectationFailed("response error");
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> broad(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{appid}')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.mqtt[?(@.pid=='{jsonp["proj_code"]?.Value<string>() ?? ""}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                if (!string.IsNullOrEmpty(jsonp["addr_code"].Value<string>()))
                {
                    await broadcast(rpath, (await QueueDAL.queryMqttHost(jsonp["addr_code"].Value<string>())).Select(rw => $"device/{rw.username}/broad"), !string.IsNullOrEmpty(jsonp["send_data"].Value<string>()) ? JObject.FromObject(new { data = jsonp["send_data"].Value<string>() }).ToString(Formatting.None) : "");
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task publish(string path, string topic, string payload)
        {
            await (JsonHelper.GetJsonValue<string>(path + ".url") + "/api/v5/publish")
                .WithBasicAuth(JsonHelper.GetJsonValue<string>(path + ".username"), JsonHelper.GetJsonValue<string>(path + ".password"))
                .PostJsonAsync(new
                {
                    topic = topic,
                    payload = payload,
                    qos = 0
                });
        }

        public static async Task broadcast(string path, IEnumerable<string> topics, string payload)
        {
            await (JsonHelper.GetJsonValue<string>(path + ".url") + "/api/v5/publish/bulk")
                .WithBasicAuth(JsonHelper.GetJsonValue<string>(path + ".username"), JsonHelper.GetJsonValue<string>(path + ".password"))
                .PostJsonAsync(topics.Select(r => new
                {
                    topic = r,
                    payload = payload,
                    qos = 0
                }));
        }
    }
}
