using IdGen;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;

namespace tksrv.lib
{
    /// <summary>
    /// JWT工具类，用于生成和验证JWT令牌
    /// </summary>
    public class JwtHelper
    {
        /// <summary>
        /// 从配置文件中读取JWT密钥
        /// </summary>
        private static readonly string JwtSecretKey = "aB7cD9eF2gH5jK8mN1pQ4rS6tU3vW0xY";

        // 使用IdGen库创建雪花算法ID生成器实例
        private static readonly IdGenerator _idGenerator = new IdGenerator(0);

        /// <summary>
        /// 生成JWT令牌
        /// </summary>
        /// <param name="pid">用户的pid</param>
        /// <param name="expireSeconds">过期时间（秒）</param>
        /// <param name="additionalClaims">额外的声明信息</param>
        /// <returns>JWT令牌字符串</returns>
        public static string GenerateToken(string pid, int expireSeconds = 86400, Dictionary<string, string> additionalClaims = null)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(JwtSecretKey);

            // 创建基本声明
            var claims = new List<Claim>
            {
                new Claim("pid", pid),
                new Claim(JwtRegisteredClaimNames.Jti, _idGenerator.CreateId().ToString())
            };

            // 添加额外的声明
            if (additionalClaims != null)
            {
                foreach (var claim in additionalClaims)
                {
                    claims.Add(new Claim(claim.Key, claim.Value));
                }
            }

            // 创建JWT描述符
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.Now.AddSeconds(expireSeconds),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            // 生成JWT令牌
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        /// <summary>
        /// 验证JWT令牌
        /// </summary>
        /// <param name="token">JWT令牌字符串</param>
        /// <returns>验证结果，true表示验证成功，false表示验证失败</returns>
        public static bool ValidateToken(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(JwtSecretKey);

                // 验证令牌
                tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从JWT令牌中获取声明值
        /// </summary>
        /// <param name="token">JWT令牌字符串</param>
        /// <param name="claimType">声明类型</param>
        /// <returns>声明值，如果令牌无效或声明不存在则返回null</returns>
        public static string GetClaimValue(string token, string claimType)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.ASCII.GetBytes(JwtSecretKey);

                // 验证令牌
                var principal = tokenHandler.ValidateToken(token, new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false,
                    ClockSkew = TimeSpan.Zero
                }, out SecurityToken validatedToken);

                // 获取声明值
                return principal.Claims.FirstOrDefault(c => c.Type == claimType)?.Value;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 从JWT令牌中获取pid
        /// </summary>
        /// <param name="token">JWT令牌字符串</param>
        /// <returns>pid值，如果令牌无效或pid不存在则返回null</returns>
        public static string GetPid(string token)
        {
            return GetClaimValue(token, "pid");
        }
    }
}