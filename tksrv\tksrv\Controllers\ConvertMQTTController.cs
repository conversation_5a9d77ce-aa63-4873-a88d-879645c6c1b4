﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/convertmqtt")]
    public class ConvertMQTTController : ControllerBase
    {
        [HttpPost, Route("getMeter")]
        public async Task<JsonResult> getMeter([FromBody] JObject json)
        {
            return new JsonResult(await ConvertMQTTBLL.getMeter(json));
        }
        
        [HttpPost, Route("setMeter")]
        public async Task<JsonResult> setMeter([FromBody] JObject json)
        {
            return new JsonResult(await ConvertMQTTBLL.setMeter(json));
        }

        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertMQTTBLL.getMeterValue(json));
        }

        [HttpPost, Route("relay")]
        public async Task<JsonResult> relay([FromBody] JObject json)
        {
            return new JsonResult(await ConvertMQTTBLL.relay(json));
        }

        [HttpPost, Route("broad")]
        public async Task<JsonResult> broad([FromBody] JObject json)
        {
            return new JsonResult(await ConvertMQTTBLL.broad(json));
        }
    }
}
