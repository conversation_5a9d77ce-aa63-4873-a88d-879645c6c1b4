# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a .NET Core 3.1 microservice designed for IoT device protocol conversion and data storage. The application serves as a gateway that:
- Receives data from various IoT devices via different protocols (MQTT, HTTP APIs)
- Converts between different device protocols (BDYB, GLXX, HBYB, HZZD, JLKJ, QSBJ, Dwin displays)
- Stores device data in MySQL database with Redis caching
- Provides REST APIs for device management and data retrieval

## Architecture

The solution follows a layered architecture pattern with these projects:

- **tksrv** (main web API): ASP.NET Core controllers and startup configuration
- **tksrv.bll** (business logic): Protocol conversion logic and business rules
- **tksrv.dal** (data access): Dapper-based MySQL queries and Redis operations
- **tksrv.model** (data models): Entity models and data transfer objects
- **tksrv.lib** (utilities): JWT helpers and common utilities
- **winapp** (console app): Background service for device monitoring

## Key Dependencies

- **Heyme.Core** & **Heyme.Data.Dapper**: Custom ORM wrapper around Dapper
- **Heyme.Utils.Logger**: Logging framework
- **Flurl.Http**: HTTP client for external API calls
- **NLog**: File-based logging
- **Newtonsoft.Json**: JSON serialization
- **System.IdentityModel.Tokens.Jwt**: JWT token handling

## Common Commands

### Build and Run
```bash
# Build entire solution
dotnet build tksrv.sln

# Run main web service (requires config.json setup)
dotnet run --project tksrv/tksrv.csproj

# Build specific project
dotnet build tksrv.bll/tksrv.bll.csproj
```

### Configuration
- Main config: `tksrv/resources/config.json`
- Contains database connection strings, bind URLs, and protocol settings
- Auto-generated from embedded resources if missing

### Database Setup
- MySQL database with connection string in config.json
- Redis for caching (connection string: kvdbStr in config)
- SQL schema: `tksrv/resources/tksrv.sql`

## Protocol Conversion Patterns

Each device protocol follows a consistent pattern:
1. **Controller** (tksrv/Controllers/Convert{Protocol}Controller.cs): REST endpoints
2. **BLL** (tksrv.bll/Convert{Protocol}BLL.cs): Protocol-specific conversion logic
3. **DAL** methods in DeviceDAL.cs for database operations
4. **Models** for device data structures

Device protocols supported:
- **BDYB**: Beidou communication devices
- **GLXX**: GPS tracking devices  
- **HBYB**: Environmental monitoring
- **HZZD**: Industrial control systems
- **JLKJ**: Energy monitoring devices
- **QSBJ**: Water quality sensors
- **MQTT**: General MQTT device gateway
- **Dwin**: Touch screen displays

## Key Configuration Notes

- JWT authentication handled via tksrv.lib/JwtHelper.cs
- Real IP extraction middleware for proxy deployments
- File logging to logs/{date}.log with different levels
- Custom JSON date format: "yyyy-MM-dd HH:mm:ss.fff"
- Database initialization via DapperManager.Initialize()

## Development Tools

The webtools/ directory contains Windows service management scripts:
- install.bat/uninstall.bat: Service installation
- start.bat/stop.bat/restart.bat: Service control
- Uses NSSM (Non-Sucking Service Manager)