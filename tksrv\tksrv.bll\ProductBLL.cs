﻿using Heyme;
using Heyme.Data.Models;
using NLog;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using tksrv.dal;
using tksrv.lib;
using tksrv.model;
using tkapi.Models;
using Flurl.Http;
using Newtonsoft.Json;

namespace tksrv.bll
{
    public class ProductBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> login(string appid, long timespan, string noncstr, string signature)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                if (Math.Abs(DateTimeHelper.GetTimeSpan() - timespan) > 1000 * 60 * 5)
                {
                    return JsonRetModel.ValErrorPreconditionFailed("时间戳不正确");
                }
                var umodel = await DeviceDAL.queryUser(appid);
                if (EncryptHelper.EncodeMD5($"appid={ appid }&noncstr={ noncstr }&timespan={ timespan }&key={ umodel.secret_key }").ToUpper() != signature.ToUpper())
                {
                    return JsonRetModel.ValErrorExpectationFailed("签名校验失败");
                }
                
                int expire = 60 * 60 * 24;
                jsonRet.result = new
                {
                    expire = expire - 60,
                    token = JwtHelper.GenerateToken("", expire)
                };
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<Stream> license(string cpud, string uuid, string ekey, string from_ip)
        {
            MemoryStream stream = new MemoryStream();
            var devk = await ProductDAL.queryDeviceKey(ekey);
            if (devk == null || devk.is_enable != 1 || ekey != "qw7zlr1kz50nev2a")
            {
                throw new ArgumentException();
            }
            if (!string.IsNullOrEmpty(devk.secret_key))
            {
                byte[] license = new byte[0];
                using (RSA rsa = new RSAProvider().CreateFromPublicKey(devk.secret_key))
                {
                    license = Encoding.UTF8.GetBytes(Convert.ToBase64String(rsa.Encrypt(Encoding.UTF8.GetBytes($"code={ cpud }"), RSAEncryptionPadding.Pkcs1)));
                }
                await ProductDAL.updateDevice(new GatewayEntity() { cpud = cpud, uuid = uuid });
                stream.Write(license, 0, license.Length);
                stream.Position = 0;
            }
            if (stream.Length <= 0)
            {
                throw new ArgumentException("license");
            }

            return stream;
        }
    }
}
