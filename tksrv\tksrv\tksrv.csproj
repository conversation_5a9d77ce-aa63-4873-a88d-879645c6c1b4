﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <Version>3.1.2</Version>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="apptools\**" />
    <Compile Remove="webtools\**" />
    <Content Remove="apptools\**" />
    <Content Remove="webtools\**" />
    <EmbeddedResource Remove="apptools\**" />
    <EmbeddedResource Remove="webtools\**" />
    <None Remove="apptools\**" />
    <None Remove="webtools\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="resources\config.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="resources\config.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Heyme.Core" Version="1.2.7" />
    <PackageReference Include="Heyme.Utils.Logger" Version="1.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="3.1.20" />
    <PackageReference Include="NLog" Version="5.3.4" />
    <PackageReference Include="System.Drawing.Common" Version="4.7.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.34.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\tksrv.bll\tksrv.bll.csproj" />
    <ProjectReference Include="..\tksrv.lib\tksrv.lib.csproj" />
    <ProjectReference Include="..\tksrv.model\tksrv.model.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>


</Project>
