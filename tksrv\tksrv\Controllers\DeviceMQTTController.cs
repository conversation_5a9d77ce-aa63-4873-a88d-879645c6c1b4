﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/mqtt")]
    public class DeviceMQTTController : ControllerBase
    {
        [HttpGet, Route("license")]
        public async Task<JsonResult> license(string imei, string imsi, string iccid, string ekey)
        {
            return new JsonResult(await DeviceMQTTBLL.license(imei ?? "", imsi ?? "", iccid ?? "", ekey));
        }

        [HttpPost, Route("auth")]
        public async Task<JsonResult> auth([FromBody] JObject json)
        {
            return new JsonResult(await DeviceMQTTBLL.auth(json["clientid"].Value<string>(), json["username"].Value<string>(), json["password"].Value<string>()));
        }

        [HttpPost, Route("result")]
        public async Task result([FromBody] JObject json)
        {
            await DeviceMQTTBLL.result(json["clientid"].Value<string>(), json["topic"].Value<string>(), json["payload"].Value<string>());
        }

        [HttpPost, Route("config")]
        public async Task config([FromBody] JObject json)
        {
            await DeviceMQTTBLL.config(json["clientid"].Value<string>(), json["topic"].Value<string>(), json["payload"].Value<string>());
        }

        [HttpPost, Route("report")]
        public async Task report([FromBody] JObject json)
        {
            await DeviceMQTTBLL.report(json["clientid"].Value<string>(), json["topic"].Value<string>(), json["payload"].Value<string>());
        }

        [HttpPost, Route("update")]
        public async Task update([FromBody] JObject json)
        {
            await DeviceMQTTBLL.update(json["clientid"].Value<string>());
        }

        [HttpPost, Route("connected")]
        public async Task connected([FromBody] JObject json)
        {
            await DeviceMQTTBLL.connected(json["clientid"].Value<string>(), json["peername"].Value<string>());
        }

        [HttpPost, Route("disconnected")]
        public async Task disconnected([FromBody] JObject json)
        {
            await DeviceMQTTBLL.disconnected(json["clientid"].Value<string>(), json["reason"].Value<string>());
        }
    }
}
