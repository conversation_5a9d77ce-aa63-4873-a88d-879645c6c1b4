﻿using Heyme;
using Heyme.Data;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Linq;
using System.Threading.Tasks;
using tksrv.dal;
using tksrv.model;
using tkapi.Models;
using NLog;
using Flurl.Http;
using Heyme.Enum.Device;
using System.Collections.Generic;
using System.Threading;
using tksrv.lib;

namespace tksrv.bll
{
    public class DeviceGLXXBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<JsonRetModel> getValueWK(JObject jsonp, string token = "")
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                string pid = "";
                if (string.IsNullOrEmpty(token))
                {
                    pid = jsonp["proj_code"]?.Value<string>() ?? "";
                    if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{jsonp["appid"].Value<string>()}')]") == null)
                    {
                        return JsonRetModel.ValErrorForbidden();
                    }
                }
                else
                {
                    pid = JwtHelper.GetPid(token);
                    if (!JwtHelper.ValidateToken(token))
                    {
                        return JsonRetModel.ValErrorMethodNotAllowed("无效的令牌");
                    }
                }
                List<object> list = new List<object>();
                string rpath = $"convert.mqtt[?(@.pid=='{pid}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                if (await QueueDAL.existMqttUser(icid) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                JsonRetModel r = null;
                string upid = "u" + Guid.NewGuid().ToString("N");
                string topic = $"device/{upid}/respone/relay";
                try
                {
                    var tcs = new TaskCompletionSource<JsonRetModel>();
                    await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                    {
                        if (!string.IsNullOrEmpty(message))
                        {
                            tcs.TrySetResult(JsonRetModel.Parse(message));
                        }
                    });
                    await ConvertMQTTBLL.publish(rpath, $"device/{icid}/request/relay", JsonConvert.SerializeObject(new { uuid = upid, data = "010301000036C420", rtime = 1000 }, Formatting.None));
                    if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                    {
                        r = tcs.Task.Result;
                    }
                }
                finally
                {
                    await DapperManager.Redis.UnsubscribeAsync(topic);
                }
                if (r != null && r.GetResult())
                {
                    byte[] recv = ProtocolHelper.ToByteArray(r.Value<string>());
                    if (ProtocolHelper.GetCRC16(recv.Take(recv.Length - 2).ToArray(), 0) != ProtocolHelper.ToUInt16L(recv, recv.Length - 2))
                    {
                        return JsonRetModel.ValErrorExpectationFailed("response error");
                    }
                    DateTime time = DateTime.Now;
                    byte[] data = recv.Skip(3).ToArray();
                    DeviceParam devpList = new DeviceParam();
                    devpList.Push(401, (ProtocolHelper.ToUInt32H(data, 44) + ProtocolHelper.ToUInt32H(data, 48) + ProtocolHelper.ToUInt32H(data, 52)) / 60m / 60m);
                    devpList.Push(402, (ProtocolHelper.ToUInt32H(data, 64) + ProtocolHelper.ToUInt32H(data, 76) + ProtocolHelper.ToUInt32H(data, 88)) / 60m / 60m);
                    devpList.Push(403, (ProtocolHelper.ToUInt32H(data, 60) + ProtocolHelper.ToUInt32H(data, 72) + ProtocolHelper.ToUInt32H(data, 84)) / 60m / 60m);
                    devpList.Push(404, (ProtocolHelper.ToUInt32H(data, 56) + ProtocolHelper.ToUInt32H(data, 68) + ProtocolHelper.ToUInt32H(data, 80)) / 60m / 60m);
                    devpList.Push(450, ProtocolHelper.ToUInt16H(data, 24) / 100m);
                    devpList.Push(451, ProtocolHelper.ToUInt16H(data, 26) * 100);
                    switch (ProtocolHelper.ToUInt16H(data, 32))
                    {
                        case 0x00:
                            devpList.Push(433, PARAMDEFINE_TME.KGZT_GB);        //关机
                            break;

                        case 0x01:
                            devpList.Push(433, PARAMDEFINE_TME.KGZT_DK);        //开机
                            break;
                    }
                    switch (ProtocolHelper.ToUInt16H(data, 34))
                    {
                        case 0x00:
                            devpList.Push(439, PARAMDEFINE_TME.YXMS_ZL);  //制冷
                            break;

                        case 0x01:
                            devpList.Push(439, PARAMDEFINE_TME.YXMS_ZR);  //制热
                            break;

                        case 0x02:
                            devpList.Push(439, PARAMDEFINE_TME.YXMS_TF);  //通风
                            break;
                    }
                    switch (ProtocolHelper.ToUInt16H(data, 36))
                    {
                        case 0x00:
                            devpList.Push(440, PARAMDEFINE_TME.FSDW_ZD);    //自动
                            break;

                        case 0x01:
                            devpList.Push(440, PARAMDEFINE_TME.FSDW_DF);    //低风
                            break;

                        case 0x02:
                            devpList.Push(440, PARAMDEFINE_TME.FSDW_ZF);    //中风
                            break;

                        case 0x03:
                            devpList.Push(440, PARAMDEFINE_TME.FSDW_GF);    //高风
                            break;
                    }
                    devpList.Push(441, ProtocolHelper.ToUInt16H(data, 38) / 10m);
                    devpList.Push(405, ProtocolHelper.ToUInt16H(data, 30) / 10m);
                    if ((ProtocolHelper.ToUInt16H(data, 28) & 0x0001) != 0)
                    {
                        devpList.Push(437, PARAMDEFINE_TME.FMZT_FK);   //阀开
                    }
                    else
                    {
                        devpList.Push(437, PARAMDEFINE_TME.FMZT_FG);   //阀关
                    }
                    if (ProtocolHelper.ToUInt16H(data, 106) != 0)
                    {
                        devpList.Push(435, PARAMDEFINE_TME.JFYX_YX);
                    }
                    else
                    {
                        devpList.Push(435, PARAMDEFINE_TME.JFYX_JZ);
                    }
                    devpList.Push(445, (ProtocolHelper.ToUInt16H(data, 40) & 0x8000) != 0 ? PARAMDEFINE_TME.GNSD_SD : PARAMDEFINE_TME.GNSD_JS);
                    devpList.Push(446, (ProtocolHelper.ToUInt16H(data, 40) & 0x0100) != 0 ? PARAMDEFINE_TME.GNSD_SD : PARAMDEFINE_TME.GNSD_JS);
                    devpList.Push(455, (ProtocolHelper.ToUInt16H(data, 40) & 0x4000) != 0 ? PARAMDEFINE_TME.GNSD_SD : PARAMDEFINE_TME.GNSD_JS);
                    devpList.Push(444, (ProtocolHelper.ToUInt16H(data, 40) & 0x0004) != 0 ? PARAMDEFINE_TME.GNSD_SD : PARAMDEFINE_TME.GNSD_JS);
                    devpList.Push(447, (ProtocolHelper.ToUInt16H(data, 40) & 0x0001) != 0 ? PARAMDEFINE_TME.GNSD_SD : PARAMDEFINE_TME.GNSD_JS);
                    devpList.Push(443, data[10]);
                    devpList.Push(442, data[11]);
                    devpList.Push(429, (decimal)(new DateTime(ProtocolHelper.ToUInt16H(data, 92), ProtocolHelper.ToUInt16H(data, 94), ProtocolHelper.ToUInt16H(data, 96), ProtocolHelper.ToUInt16H(data, 100), ProtocolHelper.ToUInt16H(data, 102), ProtocolHelper.ToUInt16H(data, 104)) - new DateTime(2000, 1, 1)).TotalSeconds);
                    foreach (var devp in devpList.ToList())
                    {
                        list.Add(new
                        {
                            param_id = devp.ID,
                            param_number = Math.Round(devp.Number, 4),
                            gather_time = time
                        });
                    }
                    jsonRet.result = list;
                }
                if (jsonRet.result == null)
                {
                    return JsonRetModel.ValErrorExpectationFailed("response error");
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setValueWK(JObject jsonp, string token = "")
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                string pid = "";
                if (string.IsNullOrEmpty(token))
                {
                    pid = jsonp["proj_code"]?.Value<string>() ?? "";
                    if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{jsonp["appid"].Value<string>()}')]") == null)
                    {
                        return JsonRetModel.ValErrorForbidden();
                    }
                }
                else
                {
                    pid = JwtHelper.GetPid(token);
                    if (!JwtHelper.ValidateToken(token))
                    {
                        return JsonRetModel.ValErrorMethodNotAllowed("无效的令牌");
                    }
                }
                string rpath = $"convert.mqtt[?(@.pid=='{pid}')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string icid = "i" + jsonp["addr_code"].Value<string>().TrimStart('i');
                if (await QueueDAL.existMqttUser(icid) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                foreach (var ctrl in jsonp["ctrl_list"].Value<string>().Split(','))
                {
                    if (string.IsNullOrEmpty(ctrl))
                    {
                        continue;
                    }
                    int id = Convert.ToInt32(ctrl.Split('=')[0]);
                    decimal vl = Convert.ToDecimal(ctrl.Split('=')[1]);
                    List<byte> byTx = new List<byte>();
                    switch (id)
                    {
                        case 433:
                            byTx.AddRange(new byte[] { 0x01, 0x06, 0x01, 0x10 });
                            switch (vl)
                            {
                                case PARAMDEFINE_TME.KGZT_GB:
                                    byTx.AddRange(new byte[] { 0x00, 0x00 });
                                    break;

                                case PARAMDEFINE_TME.KGZT_DK:
                                    byTx.AddRange(new byte[] { 0x00, 0x01 });
                                    break;

                                default:
                                    return JsonRetModel.ValErrorPreconditionFailed();
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 439:
                            byTx.AddRange(new byte[] { 0x01, 0x06, 0x01, 0x11 });
                            switch (vl)
                            {
                                case PARAMDEFINE_TME.YXMS_ZL:
                                    byTx.AddRange(new byte[] { 0x00, 0x00 });
                                    break;

                                case PARAMDEFINE_TME.YXMS_ZR:
                                    byTx.AddRange(new byte[] { 0x00, 0x01 });
                                    break;

                                case PARAMDEFINE_TME.YXMS_TF:
                                    byTx.AddRange(new byte[] { 0x00, 0x02 });
                                    break;

                                default:
                                    return JsonRetModel.ValErrorPreconditionFailed();
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 440:
                            byTx.AddRange(new byte[] { 0x01, 0x06, 0x01, 0x12 });
                            switch (vl)
                            {
                                case PARAMDEFINE_TME.FSDW_ZD:
                                    byTx.AddRange(new byte[] { 0x00, 0x00 });
                                    break;

                                case PARAMDEFINE_TME.FSDW_DF:
                                    byTx.AddRange(new byte[] { 0x00, 0x01 });
                                    break;

                                case PARAMDEFINE_TME.FSDW_ZF:
                                    byTx.AddRange(new byte[] { 0x00, 0x02 });
                                    break;

                                case PARAMDEFINE_TME.FSDW_GF:
                                    byTx.AddRange(new byte[] { 0x00, 0x03 });
                                    break;

                                default:
                                    return JsonRetModel.ValErrorPreconditionFailed();
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 441:
                            byTx.AddRange(new byte[] { 0x01, 0x06, 0x01, 0x13 });
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)(vl * 10)));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 444:
                            byTx.AddRange(new byte[] { 0x01, 0x05, 0x01, 0x02 });
                            if (vl == PARAMDEFINE_TME.GNSD_SD)
                            {
                                byTx.AddRange(new byte[] { 0xFF, 0x00 });
                            }
                            else
                            {
                                byTx.AddRange(new byte[] { 0x00, 0x00 });
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 445:
                            byTx.AddRange(new byte[] { 0x01, 0x05, 0x01, 0x0F });
                            if (vl == PARAMDEFINE_TME.GNSD_SD)
                            {
                                byTx.AddRange(new byte[] { 0xFF, 0x00 });
                            }
                            else
                            {
                                byTx.AddRange(new byte[] { 0x00, 0x00 });
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 446:
                            byTx.AddRange(new byte[] { 0x01, 0x05, 0x01, 0x08 });
                            if (vl == PARAMDEFINE_TME.GNSD_SD)
                            {
                                byTx.AddRange(new byte[] { 0xFF, 0x00 });
                            }
                            else
                            {
                                byTx.AddRange(new byte[] { 0x00, 0x00 });
                            }
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx, 0)));
                            break;

                        case 429:
                            byTx.AddRange(new byte[] { 0x01, 0x10, 0x01, 0x2E, 0x00, 0x07, 0x0E });
                            DateTime t = new DateTime(2000, 1, 1).AddSeconds((uint)vl);
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Year));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Month));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Day));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)(t.DayOfWeek != DayOfWeek.Sunday ? (int)t.DayOfWeek : 7)));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Hour));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Minute));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16H((ushort)t.Second));
                            byTx.AddRange(ProtocolHelper.GetBytesUInt16L(ProtocolHelper.GetCRC16(byTx.ToArray(), 0)));
                            break; 
                    }
                    if (byTx.Count <= 0)
                    {
                        continue;
                    }
                    JsonRetModel r = null;
                    string upid = "u" + Guid.NewGuid().ToString("N");
                    string topic = $"device/{upid}/respone/relay";
                    try
                    {
                        var tcs = new TaskCompletionSource<JsonRetModel>();
                        await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                        {
                            if (!string.IsNullOrEmpty(message))
                            {
                                tcs.TrySetResult(JsonRetModel.Parse(message));
                            }
                        });
                        await ConvertMQTTBLL.publish(rpath, $"device/{icid}/request/relay", JsonConvert.SerializeObject(new { uuid = upid, data = ProtocolHelper.ToHexString(byTx).Replace(" ", ""), rtime = 1000 }, Formatting.None));
                        if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                        {
                            r = tcs.Task.Result;
                        }
                    }
                    finally
                    {
                        await DapperManager.Redis.UnsubscribeAsync(topic);
                    }
                    bool result = false;
                    if (r != null && r.GetResult())
                    {
                        byte[] data = ProtocolHelper.ToByteArray(r.Value<string>());
                        if (ProtocolHelper.GetCRC16(data.Take(data.Length - 2).ToArray(), 0) == ProtocolHelper.ToUInt16L(data, data.Length - 2))
                        {
                            result = true;
                        }
                    }
                    if (!result)
                    {
                        jsonRet.result = id;
                        return jsonRet;
                    }
                }
                if (jsonRet.result == null)
                {
                    jsonRet.result = 0;
                } 
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public async static Task<JsonRetModel> data(string json)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device glxx data <--- " + json);
            }
            try
            {
                JObject jsonp = JObject.Parse(json);
                var devk = await ProductDAL.queryDeviceKey(jsonp["appid"].Value<string>());
                if (devk == null || devk.is_enable != 1)
                {
                    jsonRet = JsonRetModel.ValErrorForbidden();
                    return jsonRet;
                }
                string owner_code = jsonp["owner"].Value<string>().ToUpper();
                string clientid = "i" + jsonp["imei"]?.Value<string>() ?? "";
                string rpath = $"convert.glxx[?(@.projcode=='{ owner_code.EX_FirstPadLeft(10, '0') }')]";
                if (JsonHelper.SelectToken(rpath) != null && owner_code.StartsWith(JsonHelper.GetJsonValue<string>(rpath + ".projcode")))
                {
                    await DeviceDAL.updateDataDwinInfo(new EquipDwinEntity()
                    {
                        clientid = clientid,
                        bound_code = owner_code,
                        version = jsonp["version"]?.Value<string>() ?? "",
                        imei = jsonp["imei"]?.Value<string>() ?? "",
                        iccid = jsonp["iccid"]?.Value<string>() ?? ""
                    });
                    if (await DeviceDAL.existDataDwin(clientid) == null)
                    {
                        return JsonRetModel.ValErrorExpectationFailed();
                    }
                    if (string.IsNullOrEmpty(JsonHelper.GetJsonValue<string>(rpath + ".weburl")))
                    {
                        return JsonRetModel.ValErrorNotImplemented();
                    }
                    jsonRet = JsonRetModel.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".weburl") + "/getDeviceMeter").PostJsonStringAsync(JsonRetModel.ToSignature(new
                    {
                        owner_code = owner_code.EX_LastPadLeft(5, '0')
                    }, JsonHelper.GetJsonValue<string>(rpath + ".webkey"))).ReceiveString());
                    if (!jsonRet.GetResult())
                    {
                        return jsonRet;
                    }
                    var tokens = jsonRet.ToJToken();
                    jsonRet.result = new
                    {
                        itnum = 1,
                        oname = tokens.FirstOrDefault()?["owner_name"].Value<string>() ?? "",
                        total = new
                        {
                            ele = (int)(tokens.Where(rw => rw["type_id"].Value<int>() == 1)?.Sum(rw => rw["list"].Sum(r => r["param_id"].Value<int>() == 101 ? r["param_number"].Value<decimal>() : 0)) ?? 0),
                            ele_unit = "kWh",
                            wat = (int)(tokens.Where(rw => rw["type_id"].Value<int>() == 2)?.Sum(rw => rw["list"].Sum(r => r["param_id"].Value<int>() == 201 ? r["param_number"].Value<decimal>() : 0)) ?? 0),
                            wat_unit = "m3",
                            eng = (int)(tokens.Where(rw => rw["type_id"].Value<int>() == 4 || rw["type_id"].Value<int>() == 5)?.Sum(rw => rw["list"].Sum(r => (r["param_id"].Value<int>() == 424 || r["param_id"].Value<int>() == 524) ? r["param_number"].Value<decimal>() : 0)) ?? 0),
                            eng_unit = "kWh"
                        },
                        meter = tokens.Where(rw => rw["type_id"].Value<int>() == 4 || rw["type_id"].Value<int>() == 5).Select(rw =>
                        {
                            if (rw["type_id"].Value<int>() == 4)
                            {
                                return new
                                {
                                    code = JsonHelper.GetJsonValue<string>(rpath + ".projcode") + rw["meter_code"].Value<string>(),
                                    name = rw["meter_name"].Value<string>(),
                                    type = rw["type_id"].Value<int>(),
                                    punit = "kWh",
                                    number = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 424)?["param_number"].Value<decimal>().ToString("0.00") ?? "0.00",
                                    sdwd = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 441)?["param_number"].Value<int>() ?? 26,
                                    kgzt = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 433)?["param_number"].Value<int>() ?? 0,
                                    mode = (rw["access_type"].Value<int>() & 0x01 << 2) != 0 ? 1 : 2
                                };
                            }
                            else
                            {
                                return new
                                {
                                    code = JsonHelper.GetJsonValue<string>(rpath + ".projcode") + rw["meter_code"].Value<string>(),
                                    name = rw["meter_name"].Value<string>(),
                                    type = rw["type_id"].Value<int>(),
                                    punit = rw["type_id"].Value<int>() == 4 ? "kWh" : "km3",
                                    number = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 524)?["param_number"].Value<decimal>().ToString("0.00") ?? "0.00",
                                    sdwd = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 541)?["param_number"].Value<int>() ?? 26,
                                    kgzt = rw["list"].FirstOrDefault(r => r["param_id"].Value<int>() == 533)?["param_number"].Value<int>() ?? 0,
                                    mode = (rw["access_type"].Value<int>() & 0x01 << 2) != 0 ? 1 : 2
                                };
                            }
                        })
                    };
                }
                else
                {
                    jsonRet.result = new
                    {
                        itnum = 1,
                        oname = "深圳市天创达科技（演示）",
                        total = new
                        {
                            ele = 999,
                            ele_unit = "kWh",
                            wat = 999,
                            wat_unit = "m3",
                            eng = 999,
                            eng_unit = "kWh"
                        },
                        meter = new object[] {
                            new
                            {
                                code = "440301P0010000101",
                                name = "演示空调1",
                                type = 4,
                                punit = "h",
                                number = "999.99",
                                sdwd = 26,
                                kgzt = 0,
                                mode = 1
                            }
                        }
                    };
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }
            finally
            {
                if (jsonRet.result == null)
                {
                    jsonRet.result = new
                    {
                        itnum = 1
                    };
                }
            }

            return jsonRet;
        }

        public async static Task<JsonRetModel> money(string json)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device glxx money <--- " + json);
            }
            try
            {
                JObject jsonp = JObject.Parse(json);
                var devk = await ProductDAL.queryDeviceKey(jsonp["appid"].Value<string>());
                if (devk == null || devk.is_enable != 1)
                {
                    jsonRet = JsonRetModel.ValErrorForbidden();
                    return jsonRet;
                }
                string owner_code = jsonp["owner"].Value<string>().ToUpper();
                string rpath = $"convert.glxx[?(@.projcode=='{ owner_code.EX_FirstPadLeft(10, '0') }')]";
                if (JsonHelper.SelectToken(rpath) != null && owner_code.StartsWith(JsonHelper.GetJsonValue<string>(rpath + ".projcode")))
                {
                    if (string.IsNullOrEmpty(JsonHelper.GetJsonValue<string>(rpath + ".weburl")))
                    {
                        return JsonRetModel.ValErrorNotImplemented();
                    }
                    jsonRet = JsonRetModel.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".weburl") + "/getDeviceMoney").PostJsonStringAsync(JsonRetModel.ToSignature(new
                    {
                        owner_code = owner_code.EX_LastPadLeft(5, '0')
                    }, JsonHelper.GetJsonValue<string>(rpath + ".webkey"))).ReceiveString());
                    if (!jsonRet.GetResult())
                    {
                        return jsonRet;
                    }
                    var tokens = jsonRet.ToJToken();
                    if (tokens.Count() <= 0)
                    {
                        return JsonRetModel.ValErrorExpectationFailed();
                    }
                    var token = tokens.FirstOrDefault();
                    jsonRet.result = new
                    {
                        itnum = 2,
                        money = token["this_money"].Value<decimal>().ToString("0.00"),
                        last_time = token["last_time"].Value<DateTime>().ToString("yyyy-MM-dd HH:mm"),
                        this_time = token["this_time"].Value<DateTime>().ToString("yyyy-MM-dd HH:mm"),
                        meter = token["list"].Select(rw =>
                        {
                            return new
                            {
                                code = JsonHelper.GetJsonValue<string>(rpath + ".projcode") + rw["meter_code"].Value<string>(),
                                name = rw["meter_name"].Value<string>(),
                                type = rw["type_id"].Value<int>(),
                                unit = rw["this_unit"].Value<string>(),
                                data = rw["this_data"].Value<decimal>().ToString("0.00"),
                                cost = rw["this_charge"].Value<decimal>().ToString("0.00")
                            };
                        })
                    };
                }
                else
                {
                    jsonRet.result = new
                    {
                        itnum = 2,
                        money = "999.99",
                        last_time = "2023-09-01 00:00",
                        this_time = "2023-09-23 23:59",
                        meter = new object[] {
                            new
                            {
                                code = "440301P0010000101",
                                name = "演示空调1",
                                type = 4,
                                unit = "kWh",
                                data = "999.99",
                                cost = "999.99"
                            }
                        }
                    };
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }
            finally
            {
                if (jsonRet.result == null)
                {
                    jsonRet.result = new
                    {
                        itnum = 2
                    };
                }
            }
            
            return jsonRet;
        }

        public async static Task<JsonRetModel> control(string json)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device glxx control <--- " + json);
            }
            try
            {
                JObject jsonp = JObject.Parse(json);
                var devk = await ProductDAL.queryDeviceKey(jsonp["appid"].Value<string>());
                if (devk == null || devk.is_enable != 1)
                {
                    jsonRet = JsonRetModel.ValErrorForbidden();
                    return jsonRet;
                }
                string meter_code = jsonp["meter"].Value<string>().ToUpper();
                string rpath = $"convert.glxx[?(@.projcode=='{ meter_code.EX_FirstPadLeft(10, '0') }')]";
                if (meter_code.StartsWith(JsonHelper.GetJsonValue<string>(rpath + ".projcode")))
                {
                    if (string.IsNullOrEmpty(JsonHelper.GetJsonValue<string>(rpath + ".weburl")))
                    {
                        return JsonRetModel.ValErrorNotImplemented();
                    }
                    jsonRet = JsonRetModel.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".weburl") + "/setDeviceMeter").PostJsonStringAsync(JsonRetModel.ToSignature(new
                    {
                        meter_code = meter_code.EX_LastPadLeft(7, '0'),
                        ctrl_json = jsonp["ctrl_list"].Value<string>().Replace("=", ":")
                    }, JsonHelper.GetJsonValue<string>(rpath + ".webkey"))).ReceiveString());
                    if (!jsonRet.GetResult())
                    {
                        return jsonRet;
                    }
                    jsonRet.result = new
                    {
                        itnum = 3,
                        status = jsonRet.Value<int>()
                    };
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }
            finally
            {
                if (jsonRet.result == null)
                {
                    jsonRet.result = new
                    {
                        itnum = 3
                    };
                }
            }

            return jsonRet;
        }

        public async static Task<JsonRetModel> update(string json)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device glxx update <--- " + json);
            }
            try
            {
                JObject jsonp = JObject.Parse(json);
                string clientid = "i" + jsonp["imei"]?.Value<string>() ?? "";
                string owner_code = jsonp["owner"].Value<string>().ToUpper();
                string rpath = $"convert.glxx[?(@.projcode=='{ owner_code.EX_FirstPadLeft(10, '0') }')]";
                if (JsonHelper.SelectToken(rpath) != null && owner_code.StartsWith(JsonHelper.GetJsonValue<string>(rpath + ".projcode")))
                {
					int s4 = jsonp["s4"]?.Value<int>() ?? jsonp["sensor_bod"].Value<int>();
                    decimal s1 = jsonp["s1"]?.Value<decimal>() ?? jsonp["sensor_temp"].Value<decimal>(), s2 = jsonp["s2"]?.Value<decimal>() ?? jsonp["sensor_humi"].Value<decimal>(), s3 = jsonp["s3"]?.Value<decimal>() ?? jsonp["sensor_co2"].Value<decimal>();
                    await DeviceDAL.updateDataDwinReport(new EquipDwinEntity()
                    {
                        clientid = clientid,
                        bound_code = owner_code,
                        report_data = JsonConvert.SerializeObject(new
                        {
                            temp = s1,
                            humi = s2,
                            co2 = s3,
                            bod = s4
                        }, Formatting.None)
                    });
                    if (await DeviceDAL.existDataDwin(clientid) != null)
                    {
                        string pars = $"userid=&vavboxid=&roomid={owner_code.EX_LastPadLeft(5, '0')}&mancount={s4}&co2={s3}&wd={s1}&sd={s2}";
                        JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/remoteupdatavavbox?" + pars).WithHeader("Authorization", await ConvertGLXXBLL.getToken(rpath)).GetStringAsync());
                        if (JsonHelper.GetJsonValueDefault("enabled", false))
                        {
                            logger.Debug("---> " + pars, "remoteupdatavavbox");
                            logger.Debug("<--- " + ret.ToString(Formatting.None), "remoteupdatavavbox");
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }
            finally
            {
                if (jsonRet.result == null)
                {
                    jsonRet.result = new
                    {
                        itnum = 4
                    };
                }
            }

            return jsonRet;
        }

        public async static Task<JsonRetModel> moneylog(string json)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device glxx moneylog <--- " + json);
            }
            try
            {
                JObject jsonp = JObject.Parse(json);
                var devk = await ProductDAL.queryDeviceKey(jsonp["appid"].Value<string>());
                if (devk == null || devk.is_enable != 1)
                {
                    jsonRet = JsonRetModel.ValErrorForbidden();
                    return jsonRet;
                }
                string owner_code = jsonp["owner"].Value<string>().ToUpper();
                string rpath = $"convert.glxx[?(@.projcode=='{ owner_code.EX_FirstPadLeft(10, '0') }')]";
                if (JsonHelper.SelectToken(rpath) != null && owner_code.StartsWith(JsonHelper.GetJsonValue<string>(rpath + ".projcode")))
                {
                    if (string.IsNullOrEmpty(JsonHelper.GetJsonValue<string>(rpath + ".weburl")))
                    {
                        return JsonRetModel.ValErrorNotImplemented();
                    }
                    DateTime start_time = DateTime.Now.AddMonths(-6).EX_MonthMin(), end_time = DateTime.Now.AddMonths(-1).EX_MonthMax();
                    jsonRet = JsonRetModel.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".weburl") + "/getDeviceMoneyLog").PostJsonStringAsync(JsonRetModel.ToSignature(new
                    {
                        owner_code = owner_code.EX_LastPadLeft(5, '0'),
                        start_time = start_time,
                        end_time = end_time
                    }, JsonHelper.GetJsonValue<string>(rpath + ".webkey"))).ReceiveString());
                    if (!jsonRet.GetResult())
                    {
                        return jsonRet;
                    }
                    var tokens = jsonRet.ToJToken();
                    if (tokens.Count() <= 0)
                    {
                        return JsonRetModel.ValErrorExpectationFailed();
                    }
                    jsonRet.result = new
                    {
                        itnum = 5,
                        money = tokens.Sum(rw => rw["this_money"].Value<decimal>()).ToString("0.00"),
                        last_time = start_time.ToString("yyyy-MM-dd HH:mm"),
                        this_time = end_time.ToString("yyyy-MM-dd HH:mm"),
                        total = tokens.Select(rw =>
                        {
                            return new
                            {
                                time = rw["this_time"].Value<DateTime>().ToString("yyyy-MM"),
                                cost = rw["this_money"].Value<decimal>().ToString("0.00"),
                                ele = rw["list"].Sum(r => r["type_id"].Value<int>() == 1 ? r["this_data"].Value<decimal>() : 0).ToString("0.00"),
                                ele_unit = rw["list"].FirstOrDefault(r => r["type_id"].Value<int>() == 1)?["this_unit"].Value<string>() ?? "",
                                wat = rw["list"].Sum(r => r["type_id"].Value<int>() == 2 ? r["this_data"].Value<decimal>() : 0).ToString("0.00"),
                                wat_unit = rw["list"].FirstOrDefault(r => r["type_id"].Value<int>() == 2)?["this_unit"].Value<string>() ?? "",
                                eng = rw["list"].Sum(r => (r["type_id"].Value<int>() == 4 || r["type_id"].Value<int>() == 5) ? r["this_data"].Value<decimal>() : 0).ToString("0.00"),
                                eng_unit = rw["list"].FirstOrDefault(r => r["type_id"].Value<int>() == 4 || r["type_id"].Value<int>() == 5)?["this_unit"].Value<string>() ?? ""
                            };
                        })
                    };
                }
                else
                {
                    jsonRet.result = new
                    {
                        itnum = 5,
                        money = "999.99",
                        last_time = "2023-07-01 00:00",
                        this_time = "2023-09-23 23:59",
                        total = new object[] {
                            new
                            {
                                time = "2023-07",
                                cost = "999.99",
                                ele = "0.00",
                                ele_unit = "kWh",
                                wat = "0.00",
                                wat_unit = "m3",
                                eng = "999.99",
                                eng_unit = "kWh"
                            }
                        }
                    };
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }
            finally
            {
                if (jsonRet.result == null)
                {
                    jsonRet.result = new
                    {
                        itnum = 5
                    };
                }
            }

            return jsonRet;
        }
    }
}
