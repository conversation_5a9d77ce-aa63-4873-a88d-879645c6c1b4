﻿using Heyme.Data.Dapper;
using Heyme.Data.Redis;
using MySql.Data.MySqlClient;
using System.Data;
using System.Reflection;

namespace tksrv.model
{
    public class DapperManager
    {
        public static readonly RedisConner Redis = new RedisConner("tksrv.", 0);

        public static readonly DapperConner Master = new DapperConner(GetInstance);
        
        public static void Initialize(string conn = "", string kvconn = "")
        {
            Master.ConnectionString = conn;
            Redis.ConnectionString = kvconn;
            DapperMapper.SetMapper(Assembly.GetExecutingAssembly());
        }

        public static IDbConnection GetInstance(string connectionString)
        {
            return new MySqlConnection(connectionString);
        }
    }
}
