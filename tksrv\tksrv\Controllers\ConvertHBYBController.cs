﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/converthbyb")]
    public class ConvertHBYBController : ControllerBase
    {
        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertHBYBBLL.getMeterValue(json));
        }

        [HttpPost, Route("setMeterValue")]
        public async Task<JsonResult> setMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertHBYBBLL.setMeterValue(json));
        }
    }
}
