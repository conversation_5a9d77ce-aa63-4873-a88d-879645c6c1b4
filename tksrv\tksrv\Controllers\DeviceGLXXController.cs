﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/deviceglxx")]
    public class DeviceGLXXController : ControllerBase
    {
        [HttpGet, Route("login")]
        public async Task<JsonResult> login(string appid, long timespan, string noncstr, string signature)
        {
            return new JsonResult(await ProductBLL.login(appid, timespan, noncstr, signature));
        }

        [HttpPost, Route("getValueWK")]
        public async Task<JsonResult> getValueWK([FromHeader] string token, [FromBody] JObject json)
        {
            return new JsonResult(await DeviceGLXXBLL.getValueWK(json, token));
        }

        [HttpPost, Route("setValueWK")]
        public async Task<JsonResult> setValueWK([FromHeader] string token, [FromBody] JObject json)
        {
            return new JsonResult(await DeviceGLXXBLL.setValueWK(json, token));
        }

        [HttpGet, HttpPost, Route("data")]
        public async Task data()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.GetEncoding("gbk")))
            {
                await this.SetResponse((await DeviceGLXXBLL.data(await reader.ReadToEndAsync())).ToString(Formatting.None));
            }
        }

        [HttpGet, HttpPost, Route("money")]
        public async Task money()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.GetEncoding("gbk")))
            {
                await this.SetResponse((await DeviceGLXXBLL.money(await reader.ReadToEndAsync())).ToString(Formatting.None));
            }
        }

        [HttpGet, HttpPost, Route("control")]
        public async Task control()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.GetEncoding("gbk")))
            {
                await this.SetResponse((await DeviceGLXXBLL.control(await reader.ReadToEndAsync())).ToString(Formatting.None));
            }
        }

        [HttpGet, HttpPost, Route("update")]
        public async Task update()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.GetEncoding("gbk")))
            {
                await this.SetResponse((await DeviceGLXXBLL.update(await reader.ReadToEndAsync())).ToString(Formatting.None));
            }
        }

        [HttpGet, HttpPost, Route("moneylog")]
        public async Task moneylog()
        {
            using (StreamReader reader = new StreamReader(Request.Body, Encoding.GetEncoding("gbk")))
            {
                await this.SetResponse((await DeviceGLXXBLL.moneylog(await reader.ReadToEndAsync())).ToString(Formatting.None));
            }
        }

        private async Task SetResponse(string content)
        {
            var data = Encoding.GetEncoding("gbk").GetBytes(content);
            Response.ContentType = "application/json; charset=gbk";
            Response.ContentLength = data.Length;
            await Response.BodyWriter.WriteAsync(data);
            await Response.BodyWriter.FlushAsync();
        }
    }
}
