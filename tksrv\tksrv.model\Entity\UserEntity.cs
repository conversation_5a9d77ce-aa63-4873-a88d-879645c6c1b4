using System;

namespace tkapi.Models
{
    /// <summary>
    /// 用户表实体
    /// </summary>
    public class UserEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        public int sysid { get; set; }

        /// <summary>
        /// 应用ID，唯一索引
        /// </summary>
        public string appid { get; set; } = string.Empty;

        /// <summary>
        /// 密钥
        /// </summary>
        public string secret_key { get; set; } = string.Empty;

        /// <summary>
        /// 允许访问的服务，多个以逗号分隔
        /// </summary>
        public string allow { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用，1-启用，0-禁用
        /// </summary>
        public int? is_enable { get; set; } = 1;

        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? vaild_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; } = string.Empty;
    }
} 