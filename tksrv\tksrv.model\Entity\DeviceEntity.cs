using System;

namespace tkapi.Models
{
    /// <summary>
    /// 设备表实体
    /// </summary>
    public class DeviceEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        public int sysid { get; set; }

        /// <summary>
        /// 客户端ID，唯一索引
        /// </summary>
        public string clientid { get; set; } = string.Empty;

        /// <summary>
        /// 在线状态，0-离线，1-在线
        /// </summary>
        public int? is_online { get; set; } = 0;

        /// <summary>
        /// 用户名
        /// </summary>
        public string username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        public string password { get; set; } = string.Empty;

        /// <summary>
        /// IMEI
        /// </summary>
        public string imei { get; set; } = string.Empty;

        /// <summary>
        /// IMSI
        /// </summary>
        public string imsi { get; set; } = string.Empty;

        /// <summary>
        /// ICCID
        /// </summary>
        public string iccid { get; set; } = string.Empty;

        /// <summary>
        /// 主机联动节点，索引
        /// </summary>
        public string hostid { get; set; } = string.Empty;

        /// <summary>
        /// 推送Unilink，索引
        /// </summary>
        public string unilink { get; set; } = string.Empty;

        /// <summary>
        /// 推送Dispatch，索引
        /// </summary>
        public string dispatch { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        public string product { get; set; } = string.Empty;

        /// <summary>
        /// 分支版本
        /// </summary>
        public string branchv { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        public string version { get; set; } = string.Empty;

        /// <summary>
        /// 最低要求版本
        /// </summary>
        public string min_version { get; set; } = string.Empty;

        /// <summary>
        /// 遥测数据
        /// </summary>
        public string telemetry { get; set; }

        /// <summary>
        /// 属性数据
        /// </summary>
        public string attributes { get; set; }

        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime? connect_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 断开连接时间
        /// </summary>
        public DateTime? disconnect_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 断开连接原因
        /// </summary>
        public string disconnect_reason { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; } = DateTime.Now;
    }
} 