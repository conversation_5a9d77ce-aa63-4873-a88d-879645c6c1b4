cd /d %~dp0
set project_core=3.1
set current_path=%~dp0
set project_name=tksrv

if exist .\%project_name% (
  rd /q /s .\%project_name%
)
if exist .\Release\netcoreapp%project_core%\publish (
  rd /q /s .\Release\netcoreapp%project_core%\publish
)
dotnet publish ..\%project_name%.csproj --framework netcoreapp%project_core% -c Release

md .\%project_name%
md .\%project_name%\origin
copy ..\resources\tksrv.sql .\%project_name%\origin\tksrv.sql
copy ..\resources\config.json .\%project_name%\origin\config.json

md .\%project_name%\wwwroot
xcopy ..\wwwroot .\%project_name%\wwwroot /e /y

md .\%project_name%\webtools
xcopy ..\webtools .\%project_name%\webtools /e /y

md .\%project_name%\apptools
copy ..\apptools\ebuild.exe .\%project_name%\apptools\ebuild.exe

if exist .\Release\netcoreapp%project_core%\publish\runtimes ( 
  md .\%project_name%\runtimes
  md .\%project_name%\runtimes\win
  md .\%project_name%\runtimes\win-x64
  md .\%project_name%\runtimes\win-x86
  md .\%project_name%\runtimes\win-arm64
  xcopy .\Release\netcoreapp%project_core%\publish\runtimes\win .\%project_name%\runtimes\win /e /y
  xcopy .\Release\netcoreapp%project_core%\publish\runtimes\win-x64 .\%project_name%\runtimes\win-x64 /e /y
  xcopy .\Release\netcoreapp%project_core%\publish\runtimes\win-x86 .\%project_name%\runtimes\win-x86 /e /y
  xcopy .\Release\netcoreapp%project_core%\publish\runtimes\win-arm64 .\%project_name%\runtimes\win-arm64 /e /y
)

copy .\Release\netcoreapp%project_core%\publish\*.dll .\%project_name%
copy .\Release\netcoreapp%project_core%\publish\%project_name%.runtimeconfig.json .\%project_name%\%project_name%.runtimeconfig.json
copy .\Release\netcoreapp%project_core%\publish\%project_name%.deps.json .\%project_name%\%project_name%.deps.json

del /q .\%project_name%_install.zip
..\apptools\ebuild.exe --zip -o="%current_path%%project_name%_install.zip" -i="%current_path%%project_name%"
