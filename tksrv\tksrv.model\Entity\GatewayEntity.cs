using System;

namespace tkapi.Models
{
    /// <summary>
    /// 网关表实体
    /// </summary>
    public class GatewayEntity
    {
        /// <summary>
        /// 系统ID，自增主键
        /// </summary>
        public int sysid { get; set; }

        /// <summary>
        /// CPU ID，唯一索引
        /// </summary>
        public string cpud { get; set; } = string.Empty;

        /// <summary>
        /// UUID
        /// </summary>
        public string uuid { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; } = DateTime.Now;
    }
} 