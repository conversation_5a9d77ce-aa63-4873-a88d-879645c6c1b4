﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/convertqsbj")]
    public class ConvertQSBJController : ControllerBase
    {
        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertQSBJBLL.getMeterValue(json));
        }
    }
}
