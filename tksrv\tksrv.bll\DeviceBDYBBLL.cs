﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NLog;
using Org.BouncyCastle.Crypto.Generators;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using tksrv.dal;
using tksrv.model;
using tkapi.Models;
using tksrv.lib;

namespace tksrv.bll
{
    public class DeviceBDYBBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public const string DVAL_PTYPE = "KT.XXX.XBXXX";

        public static async Task<JsonRetModel> read(JObject jsonp, string token)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                if (!JwtHelper.ValidateToken(token))
                {
                    return JsonRetModel.ValErrorMethodNotAllowed("无效的令牌");
                }
                
                string pid = JwtHelper.GetPid(token);
                string rpath = $"convert.bdyb[?(@.pid=='{ pid }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                
                var dmod = await DeviceDAL.queryDataBDYBInfo(jsonp["devno"].Value<string>());
                string devno = dmod?.devno ?? jsonp["devno"].Value<string>(), ptype = dmod?.ptype ?? DVAL_PTYPE, effect = jsonp["effect"]?.Value<string>() ?? "";
                if (jsonp["effect"]?.Value<string>() == "real" || dmod == null || Math.Abs((DateTime.Now - EntityHelper.GetGridTime(dmod)).TotalSeconds) > 60 * 5)
                {
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await ConvertBDYBBLL.getToken(rpath)).PostJsonAsync(new
                    {
                        productType = ptype,
                        devNo = devno,
                        methodType = "pro07_standard_read",
                        methodContent = "grid_param_datablock"
                    }).ReceiveString());
                    if (JsonHelper.GetJsonValueDefault("enabled", false))
                    {
                        logger.Debug("receive device bdyb read <--- " + ret.ToString());
                    }
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JToken r = null;
                        string topic = ret["commandId"].Value<string>();
                        try
                        {
                            var tcs = new TaskCompletionSource<JToken>(); 
                            await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                            {
                                if (!string.IsNullOrEmpty(message))
                                {
                                    tcs.TrySetResult(JToken.Parse(message));
                                }
                            });
                            if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                            {
                                r = tcs.Task.Result;
                            }
                        }
                        finally
                        {
                            await DapperManager.Redis.UnsubscribeAsync(topic);
                        }
                        if (r != null)
                        {
                            if (r["status"]?.Value<int>() == 0)
                            {
                                var entity = EntityHelper.CreateBdybEntity(devno, ptype, "",
                                    JObject.FromObject(new { total_energy = r["result"]["combinedActiveTotalEnergy"].Value<decimal>() }).ToString(),
                                    "",
                                    JObject.FromObject(new { power = r["result"]["switchType"].Value<int>() != 0 ? 0 : 1 }).ToString());
                                await DeviceDAL.updateDataBDYBInfo(entity);
                            }
                            else
                            {
                                jsonRet.message = r["msg"]?.Value<string>() ?? "";
                            }
                        }
                        else
                        {
                            jsonRet.message = "no response, cache value";
                        }
                    }
                    else
                    {
                        jsonRet.message = ret["msg"]?.Value<string>() ?? "";
                    }
                }
                var model = await DeviceDAL.queryDataBDYBInfo(devno);
                jsonRet.result = new
                {
                    update_time = EntityHelper.GetGridTime(model),
                    total_energy = JsonHelper.GetJsonStrValueDefault<decimal>(EntityHelper.GetGridParam(model), "total_energy", 0),
                    power = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamSw(model), "power", 0),
                    kgzt = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "kgzt", 0),
                    sdwd = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "sdwd", 26),
                    yxms = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "yxms", 0),
                    fsdw = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "fsdw", 0),
                    sffs = JsonHelper.GetJsonStrValueDefault(EntityHelper.GetCtrlParamIr(model), "sffs", 0)
                };
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setpower(JObject jsonp, string token)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                if (!JwtHelper.ValidateToken(token))
                {
                    return JsonRetModel.ValErrorMethodNotAllowed("无效的令牌");
                }
                
                string pid = JwtHelper.GetPid(token);
                string rpath = $"convert.bdyb[?(@.pid=='{ pid }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                
                var dmod = await DeviceDAL.queryDataBDYBInfo(jsonp["devno"].Value<string>());
                string devno = dmod?.devno ?? jsonp["devno"].Value<string>(), ptype = dmod?.ptype ?? DVAL_PTYPE;
                JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await ConvertBDYBBLL.getToken(rpath)).PostJsonAsync(new
                {
                    productType = ptype,
                    devNo = devno,
                    methodType = "air_conditioning_control",
                    methodContent = jsonp["power"].Value<int>() != 0 ? "switch_on" : "switch_off"
                }).ReceiveString());
                if (JsonHelper.GetJsonValueDefault("enabled", false))
                {
                    logger.Debug("receive device bdyb setpower <--- " + ret.ToString());
                }
                if (ret["code"] != null && ret["code"].Value<int>() == 0)
                {
                    JToken r = null;
                    string topic = ret["commandId"].Value<string>();
                    try
                    {
                        var tcs = new TaskCompletionSource<JToken>();
                        await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                        {
                            if (!string.IsNullOrEmpty(message))
                            {
                                tcs.TrySetResult(JToken.Parse(message));
                            }
                        });
                        if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                        {
                            r = tcs.Task.Result;
                        }
                    }
                    finally
                    {
                        await DapperManager.Redis.UnsubscribeAsync(topic);
                    }
                    if (r != null)
                    {
                        if (r["status"]?.Value<int>() == 0)
                        {
                            var entity = EntityHelper.CreateBdybEntity(devno, ptype, "", "", "", 
                                JObject.FromObject(new { power = jsonp["power"].Value<int>() }).ToString());
                            await DeviceDAL.updateDataBDYBInfo(entity);
                            jsonRet.result = true;
                        }
                        else
                        {
                            jsonRet.message = r["msg"]?.Value<string>() ?? "";
                        }
                    }
                    else
                    {
                        jsonRet.message = "no response";
                    }
                }
                else
                {
                    jsonRet.message = ret["msg"]?.Value<string>() ?? "";
                }
                if (jsonRet.result == null)
                {
                    jsonRet.result = false;
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<JsonRetModel> setparam(JObject jsonp, string token)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                if (!JwtHelper.ValidateToken(token))
                {
                    return JsonRetModel.ValErrorMethodNotAllowed("无效的令牌");
                }
                
                string pid = JwtHelper.GetPid(token);
                string rpath = $"convert.bdyb[?(@.pid=='{ pid }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                
                var dmod = await DeviceDAL.queryDataBDYBInfo(jsonp["devno"].Value<string>());
                string devno = dmod?.devno ?? jsonp["devno"].Value<string>(), ptype = dmod?.ptype ?? DVAL_PTYPE, brand = !string.IsNullOrEmpty(jsonp["brand"]?.Value<string>()) ? jsonp["brand"]?.Value<string>() : dmod?.brand;
                if (jsonp["power"] != null && jsonp["power"].Value<int>() == 1)
                {
                    await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await ConvertBDYBBLL.getToken(rpath)).PostJsonAsync(new
                    {
                        productType = ptype,
                        devNo = devno,
                        methodType = "air_conditioning_control",
                        methodContent = jsonp["power"].Value<int>() != 0 ? "switch_on" : "switch_off"
                    }).ReceiveString();
                    await Task.Delay(100);  // 硬件响应完毕延迟
                }
                JObject pars = JObject.FromObject(new
                {
                    productType = ptype,
                    devNo = devno,
                    methodType = "air_controller",
                    methodContent = "infrared_set",
                    @params = new
                    {
                        firmCode = brand
                    }
                });
                switch (jsonp["kgzt"].Value<int>())
                {
                    case 0:
                        pars["params"]["switchOnOff"] = "00";
                        break;

                    case 1:
                        pars["params"]["switchOnOff"] = "01";
                        break;
                }
                pars["params"]["temperature"] = jsonp["sdwd"].Value<int>();
                switch (jsonp["yxms"].Value<int>())
                {
                    case 0:
                        pars["params"]["airMode"] = "04";
                        break;

                    case 2:
                        pars["params"]["airMode"] = "02";
                        break;

                    case 3:
                        pars["params"]["airMode"] = "01";
                        break;

                    case 4:
                        pars["params"]["airMode"] = "08";
                        break;
                }
                switch (jsonp["fsdw"].Value<int>())
                {
                    case 0:
                        pars["params"]["windSpeed"] = "00";
                        break;

                    case 1:
                        pars["params"]["windSpeed"] = "01";
                        break;

                    case 2:
                        pars["params"]["windSpeed"] = "02";
                        break;

                    case 3:
                        pars["params"]["windSpeed"] = "03";
                        break;
                }
                switch (jsonp["sffs"].Value<int>())
                {
                    case 0:
                        pars["params"]["windMode"] = "00";
                        break;

                    case 1:
                        pars["params"]["windMode"] = "01";
                        break;

                    case 2:
                        pars["params"]["windMode"] = "02";
                        break;

                    case 3:
                        pars["params"]["windMode"] = "03";
                        break;
                }
                JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + "/api/device/command").WithHeader("token", await ConvertBDYBBLL.getToken(rpath)).PostJsonStringAsync(pars.ToString()).ReceiveString());
                if (JsonHelper.GetJsonValueDefault("enabled", false))
                {
                    logger.Debug("receive device bdyb setparam <--- " + ret.ToString());
                }
                if (ret["code"] != null && ret["code"].Value<int>() == 0)
                {
                    JToken r = null;
                    string topic = ret["commandId"].Value<string>();
                    try
                    {
                        var tcs = new TaskCompletionSource<JToken>();
                        await DapperManager.Redis.SubscribeAsync(topic, (message) =>
                        {
                            if (!string.IsNullOrEmpty(message))
                            {
                                tcs.TrySetResult(JToken.Parse(message));
                            }
                        });
                        if (await Task.WhenAny(tcs.Task, Task.Delay(10000)) == tcs.Task)
                        {
                            r = tcs.Task.Result;
                        }
                    }
                    finally
                    {
                        await DapperManager.Redis.UnsubscribeAsync(topic);
                    }
                    if (r != null)
                    {
                        if (r["status"]?.Value<int>() == 0)
                        {
                            var entity = EntityHelper.CreateBdybEntity(devno, ptype, brand, "",
                                JObject.FromObject(new
                                {
                                    kgzt = jsonp["kgzt"].Value<int>(),
                                    sdwd = jsonp["sdwd"].Value<int>(),
                                    yxms = jsonp["yxms"].Value<int>(),
                                    fsdw = jsonp["fsdw"].Value<int>(),
                                    sffs = jsonp["sffs"].Value<int>()
                                }).ToString(), "");
                            await DeviceDAL.updateDataBDYBInfo(entity);
                            jsonRet.result = true;
                        }
                        else
                        {
                            jsonRet.message = r["msg"]?.Value<string>() ?? "";
                        }
                        if (jsonp["power"] != null && jsonp["power"].Value<int>() == 1)
                        {
                            var powerEntity = EntityHelper.CreateBdybEntity(devno, ptype, "", "", "",
                                JObject.FromObject(new { power = jsonp["power"].Value<int>() }).ToString());
                            await DeviceDAL.updateDataBDYBInfo(powerEntity);
                        }
                    }
                    else
                    {
                        jsonRet.message = "no response";
                    }
                }
                else
                {
                    jsonRet.message = ret["msg"]?.Value<string>() ?? "";
                }
                if (jsonRet.result == null)
                {
                    jsonRet.result = true;
                }
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task response(JObject jsonp)
        {
            if (JsonHelper.GetJsonValueDefault("enabled", false))
            {
                logger.Debug("accepted device bdyb response <--- " + jsonp.ToString());
            }
            if (jsonp["commandId"] != null && jsonp["resultParams"] != null)
            {
                await DapperManager.Redis.PublishAsync(jsonp["commandId"].Value<string>(), jsonp["resultParams"].ToString(Formatting.None));
            }
        }
    }
}
