using System;

namespace tkapi.Models
{
    /// <summary>
    /// Dwin设备实体类
    /// </summary>
    public class EquipDwinEntity
    {
        /// <summary>
        /// 系统ID（主键）
        /// </summary>
        public int sysid { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string clientid { get; set; } = string.Empty;

        /// <summary>
        /// 绑定编码
        /// </summary>
        public string bound_code { get; set; } = string.Empty;

        /// <summary>
        /// IMEI号
        /// </summary>
        public string imei { get; set; }

        /// <summary>
        /// ICCID号
        /// </summary>
        public string iccid { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string version { get; set; }

        /// <summary>
        /// 上报数据
        /// </summary>
        public string report_data { get; set; }

        /// <summary>
        /// 是否锁定
        /// </summary>
        public int? is_locked { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? create_time { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? update_time { get; set; }
    }
} 