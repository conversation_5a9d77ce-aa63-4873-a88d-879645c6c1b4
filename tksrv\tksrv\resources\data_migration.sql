-- ====================================================================
-- 数据迁移脚本 - 从旧版数据库迁移到新版表结构
-- 支持重复执行以同步最新数据
-- 执行前请确保新表结构已存在（来自table.sql）
-- ====================================================================

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 使用目标数据库
USE `tkapi`;

-- ====================================================================
-- 1. 用户数据迁移 (tb_user) - 从 UserModel + DeviceKeyModel 合并
-- ====================================================================

-- 迁移用户数据，合并 UserModel 和 DeviceKeyModel
INSERT INTO `tb_user` (
    `appid`, 
    `secret_key`, 
    `allow`, 
    `is_enable`, 
    `vaild_time`, 
    `remark`
)
SELECT DISTINCT
    COALESCE(u.appid, dk.ekey) as appid,
    COALESCE(u.secret_key, dk.secret_key, '') as secret_key,
    'default' as allow,
    CASE 
        WHEN dk.status_type = 1 THEN 1 
        ELSE 0 
    END as is_enable,
    DATE_ADD(NOW(), INTERVAL 1 YEAR) as vaild_time,
    CASE 
        WHEN u.remark IS NOT NULL AND u.remark != '' AND dk.remark IS NOT NULL AND dk.remark != '' 
        THEN CONCAT(u.remark, '; ', dk.remark)
        WHEN u.remark IS NOT NULL AND u.remark != '' THEN u.remark
        WHEN dk.remark IS NOT NULL AND dk.remark != '' THEN dk.remark
        ELSE ''
    END as remark
FROM `tksrv`.`tb_user` u
LEFT JOIN `tksrv`.`tb_device_key` dk ON u.appid = dk.ekey
WHERE COALESCE(u.appid, dk.ekey) IS NOT NULL 
  AND COALESCE(u.appid, dk.ekey) != ''
ON DUPLICATE KEY UPDATE
    secret_key = VALUES(secret_key),
    allow = VALUES(allow),
    is_enable = VALUES(is_enable),
    vaild_time = VALUES(vaild_time),
    remark = VALUES(remark);

-- 处理只存在于 DeviceKeyModel 中的数据
INSERT INTO `tb_user` (
    `appid`, 
    `secret_key`, 
    `allow`, 
    `is_enable`, 
    `vaild_time`, 
    `remark`
)
SELECT DISTINCT
    dk.ekey as appid,
    dk.secret_key,
    'default' as allow,
    CASE 
        WHEN dk.status_type = 1 THEN 1 
        ELSE 0 
    END as is_enable,
    DATE_ADD(NOW(), INTERVAL 1 YEAR) as vaild_time,
    dk.remark
FROM `tksrv`.`tb_device_key` dk
WHERE dk.ekey IS NOT NULL 
  AND dk.ekey != ''
  AND NOT EXISTS (SELECT 1 FROM `tksrv`.`tb_user` u WHERE u.appid = dk.ekey)
ON DUPLICATE KEY UPDATE
    secret_key = VALUES(secret_key),
    is_enable = VALUES(is_enable),
    remark = VALUES(remark);

-- ====================================================================
-- 2. 网关数据迁移 (tb_gateway) - 从 DeviceModel 直接映射
-- ====================================================================

INSERT INTO `tb_gateway` (
    `cpud`, 
    `uuid`, 
    `create_time`, 
    `update_time`
)
SELECT 
    `cpud`,
    COALESCE(`uuid`, '') as uuid,
    `create_time`,
    `update_time`
FROM `tksrv`.`tb_device`
WHERE `cpud` IS NOT NULL AND `cpud` != ''
ON DUPLICATE KEY UPDATE
    uuid = VALUES(uuid),
    update_time = VALUES(update_time);

-- ====================================================================
-- 3. 设备数据迁移 (tb_device) - 从 MqttModel + MqttUserModel 合并
-- ====================================================================

INSERT INTO `tb_device` (
    `clientid`,
    `is_online`,
    `username`,
    `password`,
    `imei`,
    `imsi`,
    `iccid`,
    `hostid`,
    `unilink`,
    `dispatch`,
    `product`,
    `branchv`,
    `version`,
    `min_version`,
    `telemetry`,
    `attributes`,
    `connect_time`,
    `disconnect_time`,
    `disconnect_reason`,
    `create_time`,
    `update_time`
)
SELECT DISTINCT
    m.clientid,
    CASE WHEN m.is_online = 1 THEN 1 ELSE 0 END as is_online,
    COALESCE(mu.username, '') as username,
    COALESCE(mu.password, '') as password,
    COALESCE(mu.imei, '') as imei,
    COALESCE(mu.imsi, '') as imsi,
    COALESCE(mu.iccid, '') as iccid,
    COALESCE(mu.hostid, '') as hostid,
    'default' as unilink,
    'default' as dispatch,
    COALESCE(m.product, '') as product,
    COALESCE(m.branchv, '') as branchv,
    COALESCE(m.version, '') as version,
    COALESCE(m.min_version, '1.0.4') as min_version,
    m.report_data as telemetry,
    m.config_data as attributes,
    COALESCE(m.connect_time, '2000-01-01 00:00:00') as connect_time,
    COALESCE(m.disconnect_time, '2000-01-01 00:00:00') as disconnect_time,
    COALESCE(m.disconnect_reason, '') as disconnect_reason,
    COALESCE(mu.create_time, NOW()) as create_time,
    COALESCE(mu.update_time, NOW()) as update_time
FROM `tksrv`.`tb_mqtt` m
LEFT JOIN `tksrv`.`tb_mqtt_user` mu ON m.clientid = mu.username
WHERE m.clientid IS NOT NULL AND m.clientid != ''
ON DUPLICATE KEY UPDATE
    is_online = VALUES(is_online),
    username = VALUES(username),
    password = VALUES(password),
    imei = VALUES(imei),
    imsi = VALUES(imsi),
    iccid = VALUES(iccid),
    hostid = VALUES(hostid),
    unilink = VALUES(unilink),
    dispatch = VALUES(dispatch),
    product = VALUES(product),
    branchv = VALUES(branchv),
    version = VALUES(version),
    min_version = VALUES(min_version),
    telemetry = VALUES(telemetry),
    attributes = VALUES(attributes),
    connect_time = VALUES(connect_time),
    disconnect_time = VALUES(disconnect_time),
    disconnect_reason = VALUES(disconnect_reason),
    update_time = VALUES(update_time);

-- 处理只存在于 MqttUserModel 中的设备
INSERT INTO `tb_device` (
    `clientid`,
    `is_online`,
    `username`,
    `password`,
    `imei`,
    `imsi`,
    `iccid`,
    `hostid`,
    `unilink`,
    `dispatch`,
    `product`,
    `branchv`,
    `version`,
    `min_version`,
    `telemetry`,
    `attributes`,
    `connect_time`,
    `disconnect_time`,
    `disconnect_reason`,
    `create_time`,
    `update_time`
)
SELECT DISTINCT
    mu.username as clientid,
    0 as is_online,
    mu.username,
    mu.password,
    mu.imei,
    mu.imsi,
    mu.iccid,
    mu.hostid,
    'default' as unilink,
    'default' as dispatch,
    '' as product,
    '' as branchv,
    '' as version,
    '1.0.4' as min_version,
    NULL as telemetry,
    NULL as attributes,
    '2000-01-01 00:00:00' as connect_time,
    '2000-01-01 00:00:00' as disconnect_time,
    '' as disconnect_reason,
    mu.create_time,
    mu.update_time
FROM `tksrv`.`tb_mqtt_user` mu
WHERE mu.username IS NOT NULL 
  AND mu.username != ''
  AND NOT EXISTS (SELECT 1 FROM `tksrv`.`tb_mqtt` m WHERE m.clientid = mu.username)
ON DUPLICATE KEY UPDATE
    username = VALUES(username),
    password = VALUES(password),
    imei = VALUES(imei),
    imsi = VALUES(imsi),
    iccid = VALUES(iccid),
    hostid = VALUES(hostid),
    update_time = VALUES(update_time);

-- ====================================================================
-- 4. Dwin设备数据迁移 (tb_equip_dwin) - 合并 report_data 字段
-- ====================================================================

INSERT INTO `tb_equip_dwin` (
    `clientid`,
    `bound_code`,
    `code`,
    `imei`,
    `iccid`,
    `version`,
    `report_data`,
    `is_locked`,
    `create_time`,
    `update_time`
)
SELECT 
    `clientid`,
    `bound_code`,
    COALESCE(`code`, '') as code,
    COALESCE(`imei`, '') as imei,
    COALESCE(`iccid`, '') as iccid,
    COALESCE(`version`, '') as version,
    -- 合并三个 report_data 字段为 JSON 格式
    CASE 
        WHEN `report_data` IS NOT NULL OR `report_data1` IS NOT NULL OR `report_data2` IS NOT NULL
        THEN JSON_OBJECT(
            'report_data', COALESCE(`report_data`, ''),
            'report_data1', COALESCE(`report_data1`, ''),
            'report_data2', COALESCE(`report_data2`, '')
        )
        ELSE NULL
    END as report_data,
    CASE WHEN `is_locked` = 1 THEN 1 ELSE 0 END as is_locked,
    `create_time`,
    `update_time`
FROM `tksrv`.`tb_data_dwin`
WHERE `clientid` IS NOT NULL AND `clientid` != ''
ON DUPLICATE KEY UPDATE
    bound_code = VALUES(bound_code),
    code = VALUES(code),
    imei = VALUES(imei),
    iccid = VALUES(iccid),
    version = VALUES(version),
    report_data = VALUES(report_data),
    is_locked = VALUES(is_locked),
    update_time = VALUES(update_time);

-- ====================================================================
-- 5. 北电云表设备数据迁移 (tb_equip_bdyb) - 合并遥测参数为JSON
-- ====================================================================

INSERT INTO `tb_equip_bdyb` (
    `devno`,
    `ptype`,
    `brand`,
    `unilink`,
    `telemetry`,
    `attributes`,
    `events`,
    `create_time`,
    `update_time`
)
SELECT 
    `devno`,
    COALESCE(`ptype`, '') as ptype,
    COALESCE(`brand`, '') as brand,
    'default' as unilink,
    -- 将三个参数字段合并为JSON格式
    JSON_OBJECT(
        'grid_param', COALESCE(`grid_param`, ''),
        'ctrl_param_ir', COALESCE(`ctrl_param_ir`, ''),
        'ctrl_param_sw', COALESCE(`ctrl_param_sw`, ''),
        'grid_time', COALESCE(DATE_FORMAT(`grid_time`, '%Y-%m-%d %H:%i:%s'), '2000-01-01 00:00:00'),
        'ctrl_time', COALESCE(DATE_FORMAT(`ctrl_time`, '%Y-%m-%d %H:%i:%s'), '2000-01-01 00:00:00')
    ) as telemetry,
    NULL as attributes,
    NULL as events,
    `create_time`,
    COALESCE(`ctrl_time`, `grid_time`, NOW()) as update_time
FROM `tksrv`.`tb_data_bdyb`
WHERE `devno` IS NOT NULL AND `devno` != ''
ON DUPLICATE KEY UPDATE
    ptype = VALUES(ptype),
    brand = VALUES(brand),
    unilink = VALUES(unilink),
    telemetry = VALUES(telemetry),
    update_time = VALUES(update_time);

-- ====================================================================
-- 数据迁移验证查询
-- ====================================================================

-- 验证迁移结果
SELECT 'tb_user' as table_name, COUNT(*) as count FROM `tb_user`;
SELECT 'tb_gateway' as table_name, COUNT(*) as count FROM `tb_gateway`;
SELECT 'tb_device' as table_name, COUNT(*) as count FROM `tb_device`;
SELECT 'tb_equip_dwin' as table_name, COUNT(*) as count FROM `tb_equip_dwin`;
SELECT 'tb_equip_bdyb' as table_name, COUNT(*) as count FROM `tb_equip_bdyb`;

-- 显示迁移完成信息
SELECT '数据迁移完成' as status, NOW() as completion_time;

-- ====================================================================
-- 重要说明：
-- 1. 此脚本支持重复执行，使用 ON DUPLICATE KEY UPDATE 确保数据同步
-- 2. 脚本会自动处理NULL值和空字符串
-- 3. JSON字段合并保留了原始数据结构
-- 4. 关联关系基于业务逻辑假设，如有需要请调整关联条件
-- 5. 执行前请确保目标表结构已创建
-- ====================================================================

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;