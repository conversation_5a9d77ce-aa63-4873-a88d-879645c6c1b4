﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/convertjlkj")]
    public class ConvertJLKJController : ControllerBase
    {
        [HttpPost, Route("getMeterValue")]
        public async Task<JsonResult> getMeterValue([FromBody] JObject json)
        {
            return new JsonResult(await ConvertJLKJBLL.getMeterValue(json));
        }
    }
}
