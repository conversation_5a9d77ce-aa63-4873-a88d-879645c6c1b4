﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using tksrv.bll;

namespace tksrv.Controllers
{
    [ApiController, Route("api/product")]
    public class ProductController : ControllerBase
    {
        [HttpGet, Route("license")]
        public async Task<FileResult> license(string cpud, string uuid, string ekey)
        {
            return File(await ProductBLL.license(cpud ?? "", uuid ?? "", ekey, this.Request.HttpContext.Connection.RemoteIpAddress.ToString()), "application/octet-stream", "licx");
        }
    }
}
