﻿using Flurl.Http;
using Heyme;
using Heyme.Data.Models;
using Newtonsoft.Json.Linq;
using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using tksrv.model;

namespace tksrv.bll
{
    public class ConvertHZZDBLL
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public static async Task<object> getDataValue(JObject jsonp)
        {
            int code = 200;
            List<object> list = new List<object>();
            try
            {
                string rpath = $"convert.hzzd[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) != null)
                {
                    foreach (var item in jsonp["data"])
                    {
                        string lt = (item["data_dt"] != null ? DateTimeHelper.Parse(item["data_dt"].Value<string>(), "yyyyMMdd") : DateTime.Now.AddDays(-30)).ToString("yyyy-MM-dd") + " 00:00:00";
                        string tt = (item["data_dt"] != null ? DateTimeHelper.Parse(item["data_dt"].Value<string>(), "yyyyMMdd") : DateTime.Now).ToString("yyyy-MM-dd") + " 23:59:59";
                        JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + $"/ypt/app/hdmdata/getHDMData?token={await getToken(rpath)}&type=2&row=0&page=1&timeFrom={lt}&timeTo={tt}&comAddress={item["meteraddress"].Value<string>()}&projCode={JsonHelper.GetJsonValue<string>(rpath + ".projcode")}").GetStringAsync());
                        if (ret["code"] != null && ret["code"].Value<int>() == 0)
                        {
                            decimal value = 0;
                            DateTime time = DateTime.MinValue;
                            foreach (var it in ret["data"])
                            {
                                if (it["writeTime"].Equals(JValue.CreateNull()) || it["dataValue1"].Equals(JValue.CreateNull()))
                                {
                                    continue;
                                }
                                DateTime t = it["writeTime"].Value<DateTime>();
                                if (t > time)
                                {
                                    time = t;
                                    value = it["dataValue1"].Value<decimal>();
                                }
                            }
                            list.Add(new { METERADDRESS = item["meteraddress"].Value<string>(), DATA_VALUE0 = value, DATA_DT = time.ToString("yyyy-MM-dd") });
                        }
                    }
                }
                if (list.Count <= 0)
                {
                    code = 417;
                }
            }
            catch (System.Exception ex)
            {
                code = 500;
                logger.Error(ex);
            }

            return new
            {
                code = code,
                data = list
            };
        }

        public static async Task<JsonRetModel> getMeterValue(JObject jsonp)
        {
            JsonRetModel jsonRet = new JsonRetModel();
            try
            {
                List<object> list = new List<object>();
                string appid = jsonp["appid"].Value<string>();
                if (JsonHelper.SelectToken($"convert.user[?(@.appid=='{ appid }')]") == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                string rpath = $"convert.hzzd[?(@.pid=='{ jsonp["proj_code"]?.Value<string>() ?? "" }')]";
                if (JsonHelper.SelectToken(rpath) == null)
                {
                    return JsonRetModel.ValErrorForbidden();
                }
                JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(rpath + ".url") + $"/ypt/app/hdmdata/getHDMData?token={await getToken(rpath)}&type=2&row=0&page=1&timeFrom={DateTime.Now.AddDays(-2).ToString("yyyy-MM-dd") + " 00:00:00"}&timeTo={DateTime.Now.ToString("yyyy-MM-dd") + " 23:59:59"}&comAddress={jsonp["addr_code"].Value<string>()}&projCode={JsonHelper.GetJsonValue<string>(rpath + ".projcode")}").GetStringAsync());
                if (ret["code"] != null && ret["code"].Value<int>() == 0)
                {
                    decimal value = 0;
                    DateTime time = DateTime.MinValue;
                    foreach (var it in ret["data"])
                    {
                        if (it["writeTime"].Equals(JValue.CreateNull()) || it["dataValue1"].Equals(JValue.CreateNull()))
                        {
                            continue;
                        }
                        DateTime t = it["writeTime"].Value<DateTime>();
                        if (t > time)
                        {
                            time = t;
                            value = it["dataValue1"].Value<decimal>();
                        }
                    }
                    list.Add(new
                    {
                        param_id = 201,
                        param_number = value,
                        gather_time = time
                    });
                }
                if (list.Count <= 0)
                {
                    return JsonRetModel.ValErrorExpectationFailed("invalid data");
                }
                jsonRet.result = list;
            }
            catch (System.Exception ex)
            {
                jsonRet = JsonRetModel.ValErrorException(ex);
                logger.Error(ex);
            }

            return jsonRet;
        }

        public static async Task<string> getToken(string path)
        {
            long expire = JsonHelper.GetJsonValue<long>(path + ".expire");
            string token = JsonHelper.GetJsonValue<string>(path + ".token");
            if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
            {
                return token;
            }
            string guid = Guid.NewGuid().ToString();
            string lname = "glocker." + System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.FullName + "." + JsonHelper.GetJsonValue<string>(path + ".projcode");
            if (await DapperManager.Redis.LockTakeAsync(lname, guid, TimeSpan.FromSeconds(300)))
            {
                try
                {
                    expire = JsonHelper.GetJsonValue<long>(path + ".expire");
                    token = JsonHelper.GetJsonValue<string>(path + ".token");
                    if (token != "" && expire != 0 && DateTimeHelper.GetTimeSpan() / 1000 <= expire)
                    {
                        return token;
                    }
                    string urls = $"userName={JsonHelper.GetJsonValue<string>(path + ".username")}&time={DateTimeHelper.GetTimeSpan() / 1000}&num={DateTime.Now.Ticks}";
                    urls += "&sign=" + EncryptHelper.EncodeSHA256(Encoding.UTF8.GetBytes(urls + $"&key={JsonHelper.GetJsonValue<string>(path + ".key")}")).ToLower();
                    JObject ret = JObject.Parse(await (JsonHelper.GetJsonValue<string>(path + ".url") + "/app/xcx/login?" + urls).PostUrlEncodedAsync("").ReceiveString());
                    if (ret["code"] != null && ret["code"].Value<int>() == 0)
                    {
                        JsonHelper.SetJsonValue(path + ".token", token = ret["token"].Value<string>());
                        JsonHelper.SetJsonValue(path + ".expire", expire = DateTimeHelper.GetTimeSpan() / 1000 + ret["expire"].Value<long>() - 10);
                    }
                }
                finally
                {
                    await DapperManager.Redis.LockReleaseAsync(lname, guid);
                }
            }

            return token;
        }
    }
}
